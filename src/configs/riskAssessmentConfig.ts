import { countriesConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import { EmploymentStatusType, SourceOfWealthType } from "../external-services/wealthkernelService";
import { UserDocument } from "../models/User";

export enum AmlScreeningResultEnum {
  NoHit = "NoHit",
  ImmaterialAdverseMedia = "ImmaterialAdverseMedia",
  MaterialAdverseMediaPEP = "MaterialAdverseMediaPEP",
  SanctionsListHit = "SanctionsListHit"
}

export const riskScoreMappingUK: { [key in countriesConfig.CountryCodesType]: number } = {
  // NOT in excel but in our countries config
  AQ: 100, // Antarctica
  AX: 100, // Åland Islands
  BQ: 100, // Bonaire, Sint Eustatius and Saba
  BV: 100, // Bouvet Island
  CC: 100, // Cocos (Keeling) Islands
  CX: 100, // Christmas Island
  EH: 100, // Western Sahara
  TG: 100, // Togo
  KI: 100, // Kiribati
  LS: 100, // Lesotho
  HM: 100, // Heard Island and McDonald Islands
  IO: 100, // British Indian Ocean Territory
  TF: 100, // French Southern Territories
  UM: 100, // United States Minor Outlying Islands
  VA: 100, // Holy See (Vatican City State)
  WF: 100, // Wallis and Futuna
  KM: 100, // Comoros
  GP: 100, // Guadeloupe
  YT: 100, // Mayotte
  FM: 100, // Micronesia
  NF: 100, // Norfolk Island
  PS: 100, // Palestine
  MP: 100, // Northern Mariana Islands
  PN: 100, // Pitcairn Islands
  RE: 100, // Reunion
  BL: 100, // Saint Barthelemy
  SH: 100, // Saint Helena
  MF: 100, // Saint Martin
  PM: 100, // Saint Pierre and Miquelon
  TV: 100, // Tuvalu
  TL: 100, // Timor-Leste
  TK: 100, // Tokelau
  ST: 100, // Sao Tome and Principe
  SR: 100, // Suriname
  GS: 100, // South Georgia and the South Sandwich Islands
  SJ: 100, // Svalbard and Jan Mayen
  GQ: 100, // Equatorial Guinea

  // Prohibited
  AF: 100, // Afghanistan
  BY: 100, // Belarus
  BI: 100, // Burundi
  CU: 100, // Cuba
  CD: 100, // Democratic Republic of Congo
  SY: 100, // Syria
  GN: 100, // Guinea
  HT: 100, // Haiti
  IR: 100, // Iran
  IQ: 100, // Iraq
  LB: 100, // Lebanon
  LY: 100, // Libya
  MM: 100, // Myanmar
  NI: 100, // Nicaragua
  KP: 100, // North Korea
  SO: 100, // Somalia
  SS: 100, // South Sudan
  SD: 100, // Sudan
  VE: 100, // Venezuela
  YE: 100, // Yemen
  ZW: 100, // Zimbabwe

  // High risk
  AL: 11, // Albania
  AM: 11, // Armenia
  AZ: 11, // Azerbaijan
  BB: 11, // Barbados
  BA: 11, // Bosnia-Herzegovina
  BW: 11, // Botswana
  BN: 11, // Brunei
  BF: 11, // Burkina Faso
  KH: 11, // Cambodia
  KY: 11, // Cayman Islands
  CF: 11, // Central African Rep
  CN: 11, // China
  CG: 11, // Congo
  HR: 11, // Croatia
  ER: 11, // Eritrea
  SZ: 11, // Eswantini (Swaziland)
  ET: 11, // Ethiopia
  FK: 11, // Falkland Islands
  GH: 11, // Ghana
  JM: 11, // Jamaica
  JO: 11, // Jordan
  LA: 11, // Laos
  ML: 11, // Mali
  MT: 11, // Malta
  MU: 11, // Mauritius
  MN: 11, // Mongolia
  MA: 11, // Morocco
  NR: 11, // Nauru
  MK: 11, // North Macedonia
  PK: 11, // Pakistan
  PA: 11, // Panama
  PH: 11, // Philippines
  SN: 11, // Senegal
  SX: 11, // Sint Maarten
  TR: 11, // Turkey
  UG: 11, // Uganda
  AE: 11, // United Arab Emirates
  VU: 11, // Vanuatu

  // Medium risk countries
  DZ: 6, // Algeria
  AS: 6, // American Samoa
  AD: 6, // Andorra
  AO: 6, // Angola
  AI: 6, // Anguilla
  AG: 6, // Antigua and Barbuda
  AR: 6, // Argentina
  AW: 6, // Aruba
  BS: 6, // Bahamas
  BH: 6, // Bahrain
  BD: 6, // Bangladesh
  BZ: 6, // Belize
  BJ: 6, // Benin
  BM: 6, // Bermuda
  BT: 6, // Bhutan
  BO: 6, // Bolivia
  BR: 6, // Brazil
  VG: 6, // British Virgin Islands
  BG: 6, // Bulgaria
  CM: 6, // Cameroon
  CA: 6, // Canada
  CV: 6, // Cape Verde
  TD: 6, // Chad
  CL: 6, // Chile
  CO: 6, // Colombia
  CK: 6, // Cook Islands
  CR: 6, // Costa Rica
  CI: 6, // Cote D'Ivoire
  CW: 6, // Curacao
  CY: 6, // Cyprus
  DJ: 6, // Djibouti
  DM: 6, // Dominica
  DO: 6, // Dominican Republic
  EC: 6, // Ecuador
  EG: 6, // Egypt
  SV: 6, // El Salvador
  EE: 6, // Estonia
  FO: 6, // Faroe Islands
  FJ: 6, // Fiji
  GF: 6, // French Guiana
  PF: 6, // French Polynesia
  GA: 6, // Gabon
  GM: 6, // Gambia
  GE: 6, // Georgia
  GI: 6, // Gibraltar
  GL: 6, // Greenland
  GD: 6, // Grenada
  GU: 6, // Guam
  GT: 6, // Guatemala
  GG: 6, // Guernsey
  GW: 6, // Guinea-Bissau
  GY: 6, // Guyana
  HN: 6, // Honduras
  HU: 6, // Hungary
  IS: 6, // Iceland
  IN: 6, // India
  ID: 6, // Indonesia
  IM: 6, // Isle of Man
  IL: 6, // Israel
  JP: 6, // Japan
  JE: 6, // Jersey
  KZ: 6, // Kazakhstan
  KE: 6, // Kenya
  // Kosovo, needs to be added to Shared Config
  KW: 6, // Kuwait
  KG: 6, // Kyrgyzstan
  LR: 6, // Liberia
  LI: 6, // Liechtenstein
  MO: 6, // Macau
  MG: 6, // Madagascar
  MW: 6, // Malawi
  MY: 6, // Malaysia
  MV: 6, // Maldives
  MH: 6, // Marshall Islands
  MQ: 6, // Martinique
  MR: 6, // Mauritania
  MX: 6, // Mexico
  MD: 6, // Moldova
  MC: 6, // Monaco
  ME: 6, // Montenegro
  MS: 6, // Montserrat
  MZ: 6, // Mozambique
  NA: 6, // Namibia
  NP: 6, // Nepal
  NL: 6, // Netherlands
  NC: 6, // New Caledonia
  NZ: 6, // New Zealand
  NE: 6, // Niger
  NG: 6, // Nigeria
  NU: 6, // Niue
  OM: 6, // Oman
  PW: 6, // Palau
  PG: 6, // Papua New Guinea
  PY: 6, // Paraguay
  PE: 6, // Peru
  PL: 6, // Poland
  PR: 6, // Puerto Rico
  QA: 6, // Qatar
  RO: 6, // Romania
  RU: 6, // Russia ?
  RW: 6, // Rwanda
  WS: 6, // Samoa
  SM: 6, // San Marino
  SA: 6, // Saudi Arabia
  RS: 6, // Serbia
  SC: 6, // Seychelles
  SL: 6, // Sierra Leone
  SK: 6, // Slovakia
  SI: 6, // Slovenia
  SB: 6, // Solomon Islands
  ZA: 6, // South Africa
  KR: 6, // South Korea
  LK: 6, // Sri Lanka
  KN: 6, // Saint Kitts and Nevis
  LC: 6, // Saint Lucia
  VC: 6, // Saint Vincent and the Grenadines
  CH: 6, // Switzerland
  TW: 6, // Taiwan
  TJ: 6, // Tajikistan
  TZ: 6, // Tanzania
  TH: 6, // Thailand
  TO: 6, // Tonga
  TT: 6, // Trinidad and Tobago
  TN: 6, // Tunisia
  TM: 6, // Turkmenistan
  TC: 6, // Turks and Caicos Islands
  UA: 6, // Ukraine
  US: 6, // United States
  VI: 6, // Virgin Islands, U.S.
  UY: 6, // Uruguay
  UZ: 6, // Uzbekistan
  VN: 6, // Vietnam
  ZM: 6, // Zambia

  // low risk
  AU: 0, // Australia
  AT: 0, // Austria
  BE: 0, // Belgium
  CZ: 0, // Czech Republic
  DK: 0, // Denmark
  FI: 0, // Finland
  FR: 0, // France
  DE: 0, // Germany
  GR: 0, // Greece
  HK: 0, // Hong Kong
  IE: 0, // Ireland
  IT: 0, // Italy
  LV: 0, // Latvia
  LT: 0, // Lithuania
  LU: 0, // Luxembourg
  NO: 0, // Norway
  PT: 0, // Portugal
  SG: 0, // Singapore
  ES: 0, // Spain
  SE: 0, // Sweden
  GB: 0 // United Kingdom
};

export const riskScoreMappingEU: { [key in countriesConfig.CountryCodesType]: number } = {
  // NOT in excel but in our countries config
  AQ: 100, // Antarctica
  AX: 100, // Åland Islands
  BQ: 100, // Bonaire, Sint Eustatius and Saba
  BV: 100, // Bouvet Island
  CC: 100, // Cocos (Keeling) Islands
  CX: 100, // Christmas Island
  EH: 100, // Western Sahara
  TG: 100, // Togo
  KI: 100, // Kiribati
  LS: 100, // Lesotho
  HM: 100, // Heard Island and McDonald Islands
  IO: 100, // British Indian Ocean Territory
  TF: 100, // French Southern Territories
  UM: 100, // United States Minor Outlying Islands
  VA: 100, // Holy See (Vatican City State)
  WF: 100, // Wallis and Futuna
  KM: 100, // Comoros
  GP: 100, // Guadeloupe
  YT: 100, // Mayotte
  FM: 100, // Micronesia
  NF: 100, // Norfolk Island
  PS: 100, // Palestine
  MP: 100, // Northern Mariana Islands
  PN: 100, // Pitcairn Islands
  RE: 100, // Reunion
  BL: 100, // Saint Barthelemy
  SH: 100, // Saint Helena
  MF: 100, // Saint Martin
  PM: 100, // Saint Pierre and Miquelon
  TV: 100, // Tuvalu
  TL: 100, // Timor-Leste
  TK: 100, // Tokelau
  ST: 100, // Sao Tome and Principe
  SR: 100, // Suriname
  GS: 100, // South Georgia and the South Sandwich Islands
  SJ: 100, // Svalbard and Jan Mayen
  GQ: 100, // Equatorial Guinea

  // Prohibited
  AF: 100, // Afghanistan
  BY: 100, // Belarus
  BI: 100, // Burundi
  CU: 100, // Cuba
  CD: 100, // Democratic Republic of Congo
  SY: 100, // Syria
  GN: 100, // Guinea
  HT: 100, // Haiti
  IR: 100, // Iran
  IQ: 100, // Iraq
  LB: 100, // Lebanon
  LY: 100, // Libya
  MM: 100, // Myanmar
  NI: 100, // Nicaragua
  KP: 100, // North Korea
  SO: 100, // Somalia
  SS: 100, // South Sudan
  SD: 100, // Sudan
  VE: 100, // Venezuela
  YE: 100, // Yemen
  ZW: 100, // Zimbabwe

  // High risk
  AL: 11, // Albania
  DZ: 11, // Algeria
  AO: 11, // Angola
  AM: 11, // Armenia
  AZ: 11, // Azerbaijan
  BB: 11, // Barbados
  BO: 11, // Bolivia
  BA: 11, // Bosnia-Herzegovina
  BW: 11, // Botswana
  BN: 11, // Brunei
  BG: 11, // Bulgaria
  BF: 11, // Burkina Faso
  KH: 11, // Cambodia
  CM: 11, // Cameroon
  KY: 11, // Cayman Islands
  CF: 11, // Central African Rep
  CN: 11, // China
  CG: 11, // Congo
  CI: 11, // Cote D'Ivoire
  HR: 11, // Croatia
  ER: 11, // Eritrea
  SZ: 11, // Eswantini (Swaziland)
  ET: 11, // Ethiopia
  FK: 11, // Falkland Islands
  GH: 11, // Ghana
  GI: 11, // Gibraltar
  JM: 11, // Jamaica
  JO: 11, // Jordan
  KE: 11, // Kenya
  LA: 11, // Laos
  MO: 11, // Macau
  ML: 11, // Mali
  MT: 11, // Malta
  MU: 11, // Mauritius
  MC: 11, // Monaco
  MN: 11, // Mongolia
  MA: 11, // Morocco
  MZ: 11, // Mozambique
  NA: 11, // Namibia
  NP: 11, // Nepal
  NR: 11, // Nauru
  NG: 11, // Nigeria
  MK: 11, // North Macedonia
  PK: 11, // Pakistan
  PA: 11, // Panama
  PH: 11, // Philippines
  SN: 11, // Senegal
  SX: 11, // Sint Maarten
  ZA: 11, // South Africa
  TJ: 11, // Tajikistan
  TZ: 11, // Tanzania
  TT: 11, // Trinidad and Tobago
  TR: 11, // Turkey
  UG: 11, // Uganda
  AE: 11, // United Arab Emirates
  VU: 11, // Vanuatu
  VI: 11, // Virgin Islands, U.S.
  VN: 11, // Vietnam

  // Medium risk countries
  AS: 6, // American Samoa
  AD: 6, // Andorra
  AI: 6, // Anguilla
  AG: 6, // Antigua and Barbuda
  AR: 6, // Argentina
  AW: 6, // Aruba
  BS: 6, // Bahamas
  BH: 6, // Bahrain
  BD: 6, // Bangladesh
  BZ: 6, // Belize
  BJ: 6, // Benin
  BM: 6, // Bermuda
  BT: 6, // Bhutan
  BR: 6, // Brazil
  VG: 6, // British Virgin Islands
  CA: 6, // Canada
  CV: 6, // Cape Verde
  TD: 6, // Chad
  CL: 6, // Chile
  CO: 6, // Colombia
  CK: 6, // Cook Islands
  CR: 6, // Costa Rica
  CW: 6, // Curacao
  CY: 6, // Cyprus
  DJ: 6, // Djibouti
  DM: 6, // Dominica
  DO: 6, // Dominican Republic
  EC: 6, // Ecuador
  EG: 6, // Egypt
  SV: 6, // El Salvador
  EE: 6, // Estonia
  FO: 6, // Faroe Islands
  FJ: 6, // Fiji
  GF: 6, // French Guiana
  PF: 6, // French Polynesia
  GA: 6, // Gabon
  GM: 6, // Gambia
  GE: 6, // Georgia
  GL: 6, // Greenland
  GD: 6, // Grenada
  GU: 6, // Guam
  GT: 6, // Guatemala
  GG: 6, // Guernsey
  GW: 6, // Guinea-Bissau
  GY: 6, // Guyana
  HN: 6, // Honduras
  HU: 6, // Hungary
  IS: 6, // Iceland
  IN: 6, // India
  ID: 6, // Indonesia
  IM: 6, // Isle of Man
  IL: 6, // Israel
  JP: 6, // Japan
  JE: 6, // Jersey
  KZ: 6, // Kazakhstan
  // Kosovo, needs to be added to Shared Config
  KW: 6, // Kuwait
  KG: 6, // Kyrgyzstan
  LR: 6, // Liberia
  LI: 6, // Liechtenstein
  MG: 6, // Madagascar
  MW: 6, // Malawi
  MY: 6, // Malaysia
  MV: 6, // Maldives
  MH: 6, // Marshall Islands
  MQ: 6, // Martinique
  MR: 6, // Mauritania
  MX: 6, // Mexico
  MD: 6, // Moldova
  ME: 6, // Montenegro
  MS: 6, // Montserrat
  NL: 6, // Netherlands
  NC: 6, // New Caledonia
  NZ: 6, // New Zealand
  NE: 6, // Niger
  NU: 6, // Niue
  OM: 6, // Oman
  PW: 6, // Palau
  PG: 6, // Papua New Guinea
  PY: 6, // Paraguay
  PE: 6, // Peru
  PL: 6, // Poland
  PR: 6, // Puerto Rico
  QA: 6, // Qatar
  RO: 6, // Romania
  RU: 6, // Russia ?
  RW: 6, // Rwanda
  WS: 6, // Samoa
  SM: 6, // San Marino
  SA: 6, // Saudi Arabia
  RS: 6, // Serbia
  SC: 6, // Seychelles
  SL: 6, // Sierra Leone
  SK: 6, // Slovakia
  SI: 6, // Slovenia
  SB: 6, // Solomon Islands
  KR: 6, // South Korea
  LK: 6, // Sri Lanka
  KN: 6, // Saint Kitts and Nevis
  LC: 6, // Saint Lucia
  VC: 6, // Saint Vincent and the Grenadines
  CH: 6, // Switzerland
  TW: 6, // Taiwan
  TH: 6, // Thailand
  TO: 6, // Tonga
  TN: 6, // Tunisia
  TM: 6, // Turkmenistan
  TC: 6, // Turks and Caicos Islands
  UA: 6, // Ukraine
  US: 6, // United States
  UY: 6, // Uruguay
  UZ: 6, // Uzbekistan
  ZM: 6, // Zambia

  // low risk
  AU: 0, // Australia
  AT: 0, // Austria
  BE: 0, // Belgium
  CZ: 0, // Czech Republic
  DK: 0, // Denmark
  FI: 0, // Finland
  FR: 0, // France
  DE: 0, // Germany
  GR: 0, // Greece
  HK: 0, // Hong Kong
  IE: 0, // Ireland
  IT: 0, // Italy
  LV: 0, // Latvia
  LT: 0, // Lithuania
  LU: 0, // Luxembourg
  NO: 0, // Norway
  PT: 0, // Portugal
  SG: 0, // Singapore
  ES: 0, // Spain
  SE: 0, // Sweden
  GB: 0 // United Kingdom
};

export const riskScoreMapping: Record<
  entitiesConfig.CompanyEntityEnum,
  { [key in countriesConfig.CountryCodesType]: number }
> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: riskScoreMappingUK,
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: riskScoreMappingEU
};

export const employmentRiskMappingUK: { [key in EmploymentStatusType]: number } = {
  FullTime: 0,
  PartTime: 3,
  SelfEmployed: 3,
  Unemployed: 10,
  Retired: 5,
  Student: 5,
  NotWorkingDueToIllnessOrDisability: 10,
  CarerOrParent: 10
};

export const employmentRiskMappingEU: { [key in EmploymentStatusType]: number } = {
  FullTime: 0,
  PartTime: 3,
  SelfEmployed: 3,
  Unemployed: 5,
  Retired: 5,
  Student: 5,
  NotWorkingDueToIllnessOrDisability: 5,
  CarerOrParent: 5
};

export const employmentRiskMapping: Record<
  entitiesConfig.CompanyEntityEnum,
  { [key in EmploymentStatusType]: number }
> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: employmentRiskMappingUK,
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: employmentRiskMappingEU
};

export const sourceOfWealthRiskMappingUK: { [key in SourceOfWealthType]: number } = {
  Salary: 0,
  Dividend: 2, // Assuming 'Dividend' maps to 'Dividends'
  BusinessOwnership: 2, // Assuming 'BusinessOwnership' maps to company profit
  PersonalSavings: 3, // Assuming 'PersonalSavings' maps to 'Savings'
  Inheritance: 3,
  SaleOfProperty: 4,
  Gift: 10, // Assuming 'Gift' maps to 'Other' with a score of 10
  GamblingOrLottery: 10, // Not listed, assuming a score of 10
  LegalSettlement: 10, // Not listed, assuming a score of 10
  SaleOfInvestments: 10 // Assuming 'SaleOfInvestments' maps to 'Investments (other)'
};

export const sourceOfWealthRiskMappingEU: { [key in SourceOfWealthType]: number } = {
  Salary: 0,
  Dividend: 1,
  BusinessOwnership: 2,
  PersonalSavings: 2,
  Inheritance: 2,
  SaleOfProperty: 3,
  Gift: 5,
  GamblingOrLottery: 5,
  LegalSettlement: 5,
  SaleOfInvestments: 5
};

export const sourceOfWealthRiskMapping: Record<
  entitiesConfig.CompanyEntityEnum,
  { [key in SourceOfWealthType]: number }
> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: sourceOfWealthRiskMappingUK,
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: sourceOfWealthRiskMappingEU
};

export type VolumeOfTransactionsBand = { min: number; max: number | null; score: number };

export const volumeOfTransactionsRiskBands: Record<entitiesConfig.CompanyEntityEnum, VolumeOfTransactionsBand[]> =
  {
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: [
      { min: 0, max: 1000, score: 0 },
      { min: 1000, max: 5000, score: 2 },
      { min: 5000, max: 10000, score: 5 },
      { min: 10000, max: 20000, score: 8 },
      { min: 20000, max: null, score: 10 }
    ],
    [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: [
      { min: 0, max: 1000, score: 0 },
      { min: 1000, max: 5000, score: 2 },
      { min: 5000, max: 10000, score: 4 },
      { min: 10000, max: 20000, score: 6 },
      { min: 20000, max: 100000, score: 8 },
      { min: 100000, max: null, score: 10 }
    ]
  };

export type AMLScreeningScoreConfig = {
  [AmlScreeningResultEnum.NoHit]: number;
  [AmlScreeningResultEnum.ImmaterialAdverseMedia]: number;
  [AmlScreeningResultEnum.MaterialAdverseMediaPEP]: number | ((user: UserDocument) => number);
  [AmlScreeningResultEnum.SanctionsListHit]: number;
};

export const amlScreeningScoreMapping: Record<entitiesConfig.CompanyEntityEnum, AMLScreeningScoreConfig> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: {
    [AmlScreeningResultEnum.NoHit]: 0,
    [AmlScreeningResultEnum.ImmaterialAdverseMedia]: 5,
    [AmlScreeningResultEnum.MaterialAdverseMediaPEP]: (user: UserDocument) =>
      user.nationalities.includes("GB") ? 10 : 15,
    [AmlScreeningResultEnum.SanctionsListHit]: 100
  },
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: {
    [AmlScreeningResultEnum.NoHit]: 0,
    [AmlScreeningResultEnum.ImmaterialAdverseMedia]: 3,
    [AmlScreeningResultEnum.MaterialAdverseMediaPEP]: () => 11,
    [AmlScreeningResultEnum.SanctionsListHit]: 100
  }
};
