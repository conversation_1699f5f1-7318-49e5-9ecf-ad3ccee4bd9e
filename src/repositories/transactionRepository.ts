import mongoose from "mongoose";
import {
  DepositCashTransaction,
  DepositCashTransactionDocument,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionInterface
} from "../models/Transaction";
import { DepositMethodEnum, WithdrawalMethodEnum } from "../types/transactions";

export class TransactionRepository {
  /**
   * Retrieves a withdrawal transaction by matching its bank reference within a description string.
   *
   * This method performs a case-insensitive search to find withdrawal transactions where the bank reference is
   * contained anywhere within the provided description.

   * @example
   * If a withdrawal in DB has bankReference "ABC123"
   * This would match even with description "Payment from Wealthkernel Ltd: ABC123"
   */
  public static async getPendingWithdrawalWithIntermediaryByContainedBankReference(
    description: string
  ): Promise<WithdrawalCashTransactionInterface & { _id: mongoose.Types.ObjectId }> {
    const lowercaseDescription = description.toLowerCase();

    const matches = await WithdrawalCashTransaction.aggregate([
      {
        $match: {
          bankReference: { $ne: null },
          withdrawalMethod: WithdrawalMethodEnum.WITH_INTERMEDIARY,
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": {
            $nin: ["confirmed", "rejected", "canceled", "reversed"]
          }
        }
      },
      {
        $addFields: {
          lowercaseBankReference: { $toLower: "$bankReference" },
          lowercaseDescription: lowercaseDescription
        }
      },
      {
        $match: {
          $expr: {
            $regexMatch: {
              input: "$lowercaseDescription",
              regex: "$lowercaseBankReference"
            }
          }
        }
      }
    ]);

    if (matches.length > 1) {
      throw new Error(`We have received more than 1 match for Devengo withdrawal with description ${description}`);
    }

    return matches.length > 0 ? matches[0] : null;
  }

  public static async getPendingBankTransferDepositsForBankAccount(
    bankAccountId: string
  ): Promise<DepositCashTransactionDocument[]> {
    return DepositCashTransaction.find({
      bankAccount: bankAccountId,
      status: "Pending",
      depositMethod: DepositMethodEnum.BANK_TRANSFER,
      "providers.wealthkernel.status": { $ne: "Settled" },
      "providers.wealthkernel.id": { $exists: true }
    });
  }
}
