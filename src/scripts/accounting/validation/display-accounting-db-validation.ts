import Decimal from "decimal.js";
import { User } from "../../../models/User";
import { Order } from "../../../models/Order";
import { Reward } from "../../../models/Reward";
import { Gift } from "../../../models/Gift";
import {
  DepositCashTransaction,
  WithdrawalCashTransaction,
  DividendTransaction,
  SavingsDividendTransaction
} from "../../../models/Transaction";
import { AccountingValidationService } from "../../../services/accountingValidationService";

const logEuropeanTransactions = async () => {
  try {
    console.info("Starting script to fetch accounting transactions");

    const fromDate = "2025-07-01";
    const fromDateObj = new Date("2025-07-01");

    // 1. Find all users with European entity
    const europeanUsers = await User.find({ companyEntity: "WEALTHYHOOD_EUROPE" }).select("_id");
    const europeanUserIds = europeanUsers.map((user) => user._id);

    if (europeanUserIds.length === 0) {
      console.info("No European users found.");
      return;
    }

    console.info(`Found ${europeanUserIds.length} European users.`);

    // 2. Fetch deposits (following validation service patterns)
    console.info("Fetching deposits...");

    // Stage 1: Devengo acquisition confirmed deposits
    const stage1Deposits = await DepositCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.status": "confirmed",
      "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.settledAt": { $gte: fromDateObj }
    }).populate("owner");

    // Stage 2: Devengo collection confirmed deposits (instant flow)
    const stage2DepositsRaw = await DepositCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": { $gte: fromDateObj }
    }).populate(["owner", "linkedCreditTicket"]);

    // Filter for instant flow deposits only (linkedCreditTicket with status "Credited")
    const stage2Deposits = stage2DepositsRaw.filter((deposit) => deposit.inInstantMoneyFlow);

    // Stage 3: WealthKernel settled deposits
    const stage3Deposits = await DepositCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "providers.wealthkernel.status": "Settled",
      "providers.wealthkernel.settledAt": { $gte: fromDateObj }
    }).populate("owner");

    // Direct Debit Stage 1: Direct debit collection completed
    const directDebitStage1Deposits = await DepositCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      depositMethod: { $in: ["DIRECT_DEBIT", "DIRECT_DEBIT_AND_BANK_TRANSFER"] },
      $or: [
        { "directDebit.providers.gocardless.status": { $in: ["confirmed", "paid_out"] } },
        { "directDebit.providers.wealthkernel.status": { $in: ["Collected", "Completed"] } }
      ],
      createdAt: { $gte: fromDateObj }
    }).populate("owner");

    // Direct Debit Stage 2: Direct debit settlement (WealthKernel settled)
    const directDebitStage2Deposits = await DepositCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      depositMethod: { $in: ["DIRECT_DEBIT", "DIRECT_DEBIT_AND_BANK_TRANSFER"] },
      "providers.wealthkernel.status": "Settled",
      "providers.wealthkernel.settledAt": { $gte: fromDateObj }
    }).populate("owner");

    // 3. Fetch withdrawals (following validation service patterns)
    console.info("Fetching withdrawals...");

    // Stage 1: WealthKernel settled withdrawals
    const stage1Withdrawals = await WithdrawalCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "providers.wealthkernel.status": "Settled",
      "providers.wealthkernel.settledAt": { $gte: fromDateObj }
    }).populate("owner");

    // Stage 2: Devengo outgoing confirmed withdrawals
    const stage2Withdrawals = await WithdrawalCashTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
      "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": { $gte: fromDateObj }
    }).populate("owner");

    // 4. Fetch dividends (following validation service patterns)
    console.info("Fetching dividends...");

    // Asset dividends
    const assetDividends = await DividendTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      $or: [{ "providers.wealthkernel.status": "Settled" }, { "providers.wealthkernel.status": "Matched" }],
      settledAt: { $gte: fromDateObj }
    }).populate("owner");

    // MMF dividends (savings dividends)
    const mmfDividends = await SavingsDividendTransaction.find({
      owner: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      createdAt: { $gte: fromDateObj }
    }).populate("owner");

    // 5. Fetch orders (following validation service patterns)
    console.info("Fetching orders...");

    const orders = await Order.find({
      "consideration.currency": "EUR",
      $or: [{ status: "Matched" }, { status: "Settled" }],
      filledAt: { $gte: fromDateObj }
    }).populate({
      path: "transaction",
      match: {
        owner: { $in: europeanUserIds },
        category: {
          $in: [
            "AssetTransaction",
            "RebalanceTransaction",
            "SavingsTopupTransaction",
            "SavingsWithdrawalTransaction"
          ]
        }
      }
    });

    // 6. Fetch rewards (following validation service patterns)
    console.info("Fetching rewards...");

    // Reward deposits settled
    const rewardDeposits = await Reward.find({
      targetUser: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "deposit.providers.wealthkernel.status": "Settled",
      "deposit.providers.wealthkernel.submittedAt": { $gte: fromDateObj }
    }).populate("targetUser");

    // Reward orders settled
    const rewardOrders = await Reward.find({
      targetUser: { $in: europeanUserIds },
      "consideration.currency": "EUR",
      "order.providers.wealthkernel.status": "Matched",
      "order.providers.wealthkernel.submittedAt": { $gte: fromDateObj }
    }).populate("targetUser");

    // 7. Fetch gifts (following validation service patterns)
    console.info("Fetching gifts...");

    // Gift deposits settled - matches validation service exactly
    const giftDeposits = await Gift.find({
      "consideration.currency": "EUR",
      "deposit.providers.wealthkernel.status": "Settled",
      "deposit.providers.wealthkernel.submittedAt": { $gte: fromDateObj }
    });

    // 8. Display results
    console.log(`\n=== EUR Accounting-Relevant Data for European Users (from ${fromDate}) ===`);

    console.log("\n--- Deposits ---");
    console.log("Bank Transfer Deposits:");
    console.log(`  Stage 1 (Devengo acquisition confirmed): ${stage1Deposits.length} transactions`);
    console.log(`  Stage 2 (Devengo collection confirmed - instant flow): ${stage2Deposits.length} transactions`);
    console.log(`  Stage 3 (WealthKernel settled): ${stage3Deposits.length} transactions`);
    console.log("Direct Debit Deposits:");
    console.log(`  Stage 1 (Collection completed): ${directDebitStage1Deposits.length} transactions`);
    console.log(`  Stage 2 (WealthKernel settled): ${directDebitStage2Deposits.length} transactions`);

    console.log("\n--- Withdrawals ---");
    console.log(`Stage 1 (WealthKernel settled): ${stage1Withdrawals.length} transactions`);
    console.log(`Stage 2 (Devengo outgoing confirmed): ${stage2Withdrawals.length} transactions`);

    console.log("\n--- Dividends ---");
    console.log(`Asset dividends: ${assetDividends.length} transactions`);
    console.log(`MMF dividends: ${mmfDividends.length} transactions`);

    console.log("\n--- Orders ---");
    console.log(`Valid orders: ${orders.length} orders`);

    console.log("\n--- Rewards ---");
    console.log(`Reward deposits: ${rewardDeposits.length} rewards`);
    console.log(`Reward orders: ${rewardOrders.length} rewards`);

    console.log("\n--- Gifts ---");
    console.log(`Gift deposits: ${giftDeposits.length} gifts`);

    // Summary
    const totalTransactions =
      stage1Deposits.length +
      stage2Deposits.length +
      stage3Deposits.length +
      directDebitStage1Deposits.length +
      directDebitStage2Deposits.length +
      stage1Withdrawals.length +
      stage2Withdrawals.length +
      assetDividends.length +
      mmfDividends.length +
      orders.length +
      rewardDeposits.length +
      rewardOrders.length +
      giftDeposits.length;

    console.log("\n=== Summary ===");
    console.log(`Total accounting-relevant items found: ${totalTransactions}`);
    console.log(
      `- Deposits: ${stage1Deposits.length + stage2Deposits.length + stage3Deposits.length + directDebitStage1Deposits.length + directDebitStage2Deposits.length}`
    );
    console.log(`- Withdrawals: ${stage1Withdrawals.length + stage2Withdrawals.length}`);
    console.log(`- Dividends: ${assetDividends.length + mmfDividends.length}`);
    console.log(`- Orders: ${orders.length}`);
    console.log(`- Rewards: ${rewardDeposits.length + rewardOrders.length}`);
    console.log(`- Gifts: ${giftDeposits.length}`);

    console.info("Transaction fetching completed successfully.");
  } catch (error) {
    console.error("Error executing transaction fetching:", { data: { error: error.toString() } });
  }
};

async function validateDepositsAgainstLedger() {
  const fromDate = "2025-07-01";

  try {
    // 8. Validate deposits against ledger entries using AccountingValidationService
    console.log("\n=== DEPOSIT LEDGER VALIDATION ===");
    console.info("Validating deposits against ledger entries...");

    const depositValidationResults = await AccountingValidationService.validateDepositsDbWithLedger(fromDate);

    if (depositValidationResults && depositValidationResults.length > 0) {
      depositValidationResults.forEach((result) => {
        console.log(`\n--- ${result.transactionType.toUpperCase()} VALIDATION ---`);
        console.log(`Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
        console.log(`Transactions: ${result.transactionCount}`);
        console.log(`DB Total: €${result.dbTotalAmount.toFixed(2)}`);
        console.log(`Ledger Total: €${result.ledgerTotalAmount.toFixed(2)}`);
        console.log(`Difference: €${result.difference.toFixed(2)}`);
        console.log(`Ledger Entries: ${result.ledgerEntryCount}`);

        if (result.discrepancies && result.discrepancies.length > 0) {
          console.log(`\nDiscrepancies (${result.discrepancies.length}):`);
          result.discrepancies.forEach((discrepancy, index) => {
            console.log(`  ${index + 1}. Transaction ${discrepancy.transactionId}:`);
            console.log(`     DB: €${discrepancy.dbAmount.toFixed(2)}`);
            console.log(`     Ledger: €${discrepancy.ledgerAmount.toFixed(2)}`);
            console.log(`     Diff: €${discrepancy.difference.toFixed(2)}`);
            console.log(`     Issue: ${discrepancy.description}`);
          });
        }
      });

      // Summary of validation results
      const validStages = depositValidationResults.filter((result) => result.isValid).length;
      const totalStages = depositValidationResults.length;
      console.log("\n=== DEPOSIT VALIDATION SUMMARY ===");
      console.log(`Valid stages: ${validStages}/${totalStages}`);

      if (validStages === totalStages) {
        console.log("🎉 All deposit stages have correct ledger entries!");
      } else {
        console.log("⚠️  Some deposit stages have missing or incorrect ledger entries.");
      }
    } else {
      console.log("No deposit validation results returned.");
    }

    console.info("Deposit validation completed successfully.");
  } catch (error) {
    console.error("Error during deposit validation:", { data: { error: error.toString() } });
  }
}

async function validateWithdrawalsAgainstLedger() {
  const fromDate = "2025-07-01";

  try {
    console.log("\n=== WITHDRAWAL LEDGER VALIDATION ===");
    console.info("Validating withdrawals against ledger entries...");

    const withdrawalValidationResults =
      await AccountingValidationService.validateWithdrawalsDbWithLedger(fromDate);

    if (withdrawalValidationResults && withdrawalValidationResults.length > 0) {
      withdrawalValidationResults.forEach((result) => {
        console.log(`\n--- ${result.transactionType.toUpperCase()} VALIDATION ---`);
        console.log(`Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
        console.log(`Transactions: ${result.transactionCount}`);
        console.log(`DB Total: €${result.dbTotalAmount.toFixed(2)}`);
        console.log(`Ledger Total: €${result.ledgerTotalAmount.toFixed(2)}`);
        console.log(`Difference: €${result.difference.toFixed(2)}`);
        console.log(`Ledger Entries: ${result.ledgerEntryCount}`);

        if (result.discrepancies && result.discrepancies.length > 0) {
          console.log(`\nDiscrepancies (${result.discrepancies.length}):`);
          result.discrepancies.forEach((discrepancy, index) => {
            console.log(`  ${index + 1}. Transaction ${discrepancy.transactionId}:`);
            console.log(`     DB: €${discrepancy.dbAmount.toFixed(2)}`);
            console.log(`     Ledger: €${discrepancy.ledgerAmount.toFixed(2)}`);
            console.log(`     Diff: €${discrepancy.difference.toFixed(2)}`);
            console.log(`     Issue: ${discrepancy.description}`);
          });
        }
      });

      // Summary of validation results
      const validStages = withdrawalValidationResults.filter((result) => result.isValid).length;
      const totalStages = withdrawalValidationResults.length;
      console.log("\n=== WITHDRAWAL VALIDATION SUMMARY ===");
      console.log(`Valid stages: ${validStages}/${totalStages}`);

      if (validStages === totalStages) {
        console.log("🎉 All withdrawal stages have correct ledger entries!");
      } else {
        console.log("⚠️  Some withdrawal stages have missing or incorrect ledger entries.");
      }
    } else {
      console.log("No withdrawal validation results returned.");
    }

    console.info("Withdrawal validation completed successfully.");
  } catch (error) {
    console.error("Error during withdrawal validation:", { data: { error: error.toString() } });
  }
}

async function validateOrdersAgainstLedger() {
  const fromDate = "2025-07-01";

  try {
    console.log("\n=== ORDER LEDGER VALIDATION ===");
    console.info("Validating orders against ledger entries...");

    const orderValidationResults = await AccountingValidationService.validateOrdersDbWithLedger(fromDate);

    if (orderValidationResults && orderValidationResults.length > 0) {
      orderValidationResults.forEach((result) => {
        console.log(`\n--- ${result.transactionType.toUpperCase()} VALIDATION ---`);
        console.log(`Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
        console.log(`Transactions: ${result.transactionCount}`);
        console.log(`DB Total: €${result.dbTotalAmount.toFixed(2)}`);
        console.log(`Ledger Total: €${result.ledgerTotalAmount.toFixed(2)}`);
        console.log(`Difference: €${result.difference.toFixed(2)}`);
        console.log(`Ledger Entries: ${result.ledgerEntryCount}`);

        if (result.discrepancies && result.discrepancies.length > 0) {
          console.log(`\nDiscrepancies (${result.discrepancies.length}):`);
          result.discrepancies.forEach((discrepancy, index) => {
            console.log(`  ${index + 1}. Transaction ${discrepancy.transactionId}:`);
            console.log(`     DB: €${discrepancy.dbAmount.toFixed(2)}`);
            console.log(`     Ledger: €${discrepancy.ledgerAmount.toFixed(2)}`);
            console.log(`     Diff: €${discrepancy.difference.toFixed(2)}`);
            console.log(`     Issue: ${discrepancy.description}`);
          });
        }
      });

      // Summary of validation results
      const validOrders = orderValidationResults.filter((result) => result.isValid).length;
      const totalOrderTypes = orderValidationResults.length;
      console.log("\n=== ORDER VALIDATION SUMMARY ===");
      console.log(`Valid order types: ${validOrders}/${totalOrderTypes}`);

      if (validOrders === totalOrderTypes) {
        console.log("🎉 All order types have correct ledger entries!");
      } else {
        console.log("⚠️  Some order types have missing or incorrect ledger entries.");
      }
    } else {
      console.log("No order validation results returned.");
    }

    console.info("Order validation completed successfully.");
  } catch (error) {
    console.error("Error during order validation:", { data: { error: error.toString() } });
  }
}

async function validateRewardsAgainstLedger() {
  const fromDate = "2025-07-01";

  try {
    console.log("\n=== REWARD LEDGER VALIDATION ===");
    console.info("Validating rewards against ledger entries...");

    const rewardValidationResults = await AccountingValidationService.validateRewardsDbWithLedger(fromDate);

    if (rewardValidationResults && rewardValidationResults.length > 0) {
      rewardValidationResults.forEach((result) => {
        console.log(`\n--- ${result.transactionType.toUpperCase()} VALIDATION ---`);
        console.log(`Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
        console.log(`Transactions: ${result.transactionCount}`);
        console.log(`DB Total: €${result.dbTotalAmount.toFixed(2)}`);
        console.log(`Ledger Total: €${result.ledgerTotalAmount.toFixed(2)}`);
        console.log(`Difference: €${result.difference.toFixed(2)}`);
        console.log(`Ledger Entries: ${result.ledgerEntryCount}`);

        if (result.discrepancies && result.discrepancies.length > 0) {
          console.log(`\nDiscrepancies (${result.discrepancies.length}):`);
          result.discrepancies.forEach((discrepancy, index) => {
            console.log(`  ${index + 1}. Transaction ${discrepancy.transactionId}:`);
            console.log(`     DB: €${discrepancy.dbAmount.toFixed(2)}`);
            console.log(`     Ledger: €${discrepancy.ledgerAmount.toFixed(2)}`);
            console.log(`     Diff: €${discrepancy.difference.toFixed(2)}`);
            console.log(`     Issue: ${discrepancy.description}`);
          });
        }
      });

      // Summary of validation results
      const validRewards = rewardValidationResults.filter((result) => result.isValid).length;
      const totalRewardTypes = rewardValidationResults.length;
      console.log("\n=== REWARD VALIDATION SUMMARY ===");
      console.log(`Valid reward types: ${validRewards}/${totalRewardTypes}`);

      if (validRewards === totalRewardTypes) {
        console.log("🎉 All reward types have correct ledger entries!");
      } else {
        console.log("⚠️  Some reward types have missing or incorrect ledger entries.");
      }
    } else {
      console.log("No reward validation results returned.");
    }

    console.info("Reward validation completed successfully.");
  } catch (error) {
    console.error("Error during reward validation:", { data: { error: error.toString() } });
  }
}

async function displayOrderFeeDetails() {
  const fromDateObj = new Date("2025-07-01");

  try {
    console.log("\n=== ORDER FEE DETAILS ===");
    console.info("Fetching order fee details for EU orders...");

    // 2. Fetch orders (following the same pattern as in logEuropeanTransactions)
    const orders = await Order.find({
      "consideration.currency": "EUR",
      $or: [{ status: "Matched" }, { status: "Settled" }],
      filledAt: { $gte: fromDateObj }
    }).populate({
      path: "transaction",
      match: {
        category: {
          $in: [
            "AssetTransaction",
            "RebalanceTransaction",
            "SavingsTopupTransaction",
            "SavingsWithdrawalTransaction"
          ]
        }
      }
    });

    // Filter out orders where transaction is null (not matching our criteria)
    const validOrders = orders.filter((order) => order.transaction !== null);

    if (validOrders.length === 0) {
      console.info("No valid EU orders found for the specified date range.");
      return;
    }

    // Sort orders by filledAt date
    validOrders.sort((a, b) => {
      const dateA = a.filledAt ? new Date(a.filledAt).getTime() : 0;
      const dateB = b.filledAt ? new Date(b.filledAt).getTime() : 0;
      return dateA - dateB;
    });

    console.log(`\nFound ${validOrders.length} eligible EU orders\n`);

    // 3. Display fee details for each order
    console.log(
      "Order ID".padEnd(26) +
        "Filled At".padEnd(12) +
        "Side".padEnd(6) +
        "WH FX Fee".padEnd(12) +
        "WH Realtime".padEnd(15) +
        "Broker FX Fee".padEnd(15) +
        "Total All Fees".padEnd(15) +
        "Settlement Amount"
    );
    console.log("-".repeat(118));

    let totalWhFxFees = new Decimal(0);
    let totalWhRealtimeFees = new Decimal(0);
    let totalBrokerFees = new Decimal(0);
    let totalSettlementAmount = new Decimal(0);

    for (const order of validOrders) {
      const orderId = order.id;
      const filledAt = order.filledAt ? new Date(order.filledAt).toISOString().split("T")[0] : "N/A";
      const side = order.side;

      // Wealthyhood fees
      const whFxFee = new Decimal(order.fees?.fx?.amount || 0);
      const whRealtimeFee = new Decimal(order.fees?.realtimeExecution?.amount || 0);
      const totalWhFees = whFxFee.plus(whRealtimeFee);

      // Broker fees
      const brokerFxFee = new Decimal(order.providers?.wealthkernel?.accountingBrokerFxFee || 0);

      // Total all fees (WH + Broker)
      const totalAllFees = totalWhFees.plus(brokerFxFee);

      // Settlement amount (in EUR, converted from cents)
      const settlementAmount = new Decimal(order.consideration.amount || 0).div(100);

      // Display the row
      console.log(
        orderId.padEnd(26) +
          filledAt.padEnd(12) +
          side.padEnd(6) +
          `€${whFxFee.toFixed(2)}`.padEnd(12) +
          `€${whRealtimeFee.toFixed(2)}`.padEnd(15) +
          `€${brokerFxFee.toFixed(2)}`.padEnd(15) +
          `€${totalAllFees.toFixed(2)}`.padEnd(15) +
          `€${settlementAmount.toFixed(2)}`
      );

      // Add to totals
      totalWhFxFees = totalWhFxFees.plus(whFxFee);
      totalWhRealtimeFees = totalWhRealtimeFees.plus(whRealtimeFee);
      totalBrokerFees = totalBrokerFees.plus(brokerFxFee);
      totalSettlementAmount = totalSettlementAmount.plus(settlementAmount);
    }

    // Display totals
    console.log("-".repeat(118));
    console.log(
      "TOTALS".padEnd(44) +
        `€${totalWhFxFees.toFixed(2)}`.padEnd(12) +
        `€${totalWhRealtimeFees.toFixed(2)}`.padEnd(15) +
        `€${totalBrokerFees.toFixed(2)}`.padEnd(15) +
        `€${totalWhFxFees.plus(totalWhRealtimeFees).plus(totalBrokerFees).toFixed(2)}`.padEnd(15) +
        `€${totalSettlementAmount.toFixed(2)}`
    );

    console.log("\n=== FEE SUMMARY ===");
    console.log(`Total Wealthyhood FX Fees: €${totalWhFxFees.toFixed(2)}`);
    console.log(`Total Wealthyhood Realtime Fees: €${totalWhRealtimeFees.toFixed(2)}`);
    console.log(`Total Broker FX Fees: €${totalBrokerFees.toFixed(2)}`);
    console.log(`Total Settlement Amount: €${totalSettlementAmount.toFixed(2)}`);

    const totalAllFees = totalWhFxFees.plus(totalWhRealtimeFees).plus(totalBrokerFees);
    console.log(`Total All Fees: €${totalAllFees.toFixed(2)}`);

    const feePercentage = totalSettlementAmount.isZero() ? 0 : totalAllFees.div(totalSettlementAmount).mul(100);
    console.log(`Fee Percentage of Settlement: ${feePercentage.toFixed(4)}%`);

    console.info("Order fee details completed successfully.");
  } catch (error) {
    console.error("Error fetching order fee details:", { data: { error: error.toString() } });
  }
}

async function validateGiftsAgainstLedger() {
  const fromDate = "2025-07-01";

  try {
    console.log("\n=== GIFT LEDGER VALIDATION ===");
    console.info("Validating gifts against ledger entries...");

    const giftValidationResults = await AccountingValidationService.validateGiftsDbWithLedger(fromDate);

    if (giftValidationResults && giftValidationResults.length > 0) {
      giftValidationResults.forEach((result) => {
        console.log(`\n--- ${result.transactionType.toUpperCase()} VALIDATION ---`);
        console.log(`Status: ${result.isValid ? "✅ VALID" : "❌ INVALID"}`);
        console.log(`Transactions: ${result.transactionCount}`);
        console.log(`DB Total: €${result.dbTotalAmount.toFixed(2)}`);
        console.log(`Ledger Total: €${result.ledgerTotalAmount.toFixed(2)}`);
        console.log(`Difference: €${result.difference.toFixed(2)}`);
        console.log(`Ledger Entries: ${result.ledgerEntryCount}`);

        if (result.discrepancies && result.discrepancies.length > 0) {
          console.log(`\nDiscrepancies (${result.discrepancies.length}):`);
          result.discrepancies.forEach((discrepancy, index) => {
            console.log(`  ${index + 1}. Transaction ${discrepancy.transactionId}:`);
            console.log(`     DB: €${discrepancy.dbAmount.toFixed(2)}`);
            console.log(`     Ledger: €${discrepancy.ledgerAmount.toFixed(2)}`);
            console.log(`     Diff: €${discrepancy.difference.toFixed(2)}`);
            console.log(`     Issue: ${discrepancy.description}`);
          });
        }
      });

      // Summary of validation results
      const validGifts = giftValidationResults.filter((result) => result.isValid).length;
      const totalGiftTypes = giftValidationResults.length;
      console.log("\n=== GIFT VALIDATION SUMMARY ===");
      console.log(`Valid gift types: ${validGifts}/${totalGiftTypes}`);

      if (validGifts === totalGiftTypes) {
        console.log("🎉 All gift types have correct ledger entries!");
      } else {
        console.log("⚠️  Some gift types have missing or incorrect ledger entries.");
      }
    } else {
      console.log("No gift validation results returned.");
    }

    console.info("Gift validation completed successfully.");
  } catch (error) {
    console.error("Error during gift validation:", { data: { error: error.toString() } });
  }
}

(async () => {
  try {
    await logEuropeanTransactions();
    await validateDepositsAgainstLedger();
    await validateWithdrawalsAgainstLedger();
    await validateOrdersAgainstLedger();
    await validateRewardsAgainstLedger();
    await validateGiftsAgainstLedger();

    // await displayOrderFeeDetails();

    console.log("\n🎯 All validation processes completed!");
    process.exit(0);
  } catch (error) {
    console.log("error");
    console.error(error);
    process.exit(1);
  }
})();
