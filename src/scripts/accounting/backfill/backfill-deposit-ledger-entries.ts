import { DepositCashTransaction, TransactionPopulationFieldsEnum } from "../../../models/Transaction";
import { AccountingRecordIndex } from "../../../models/AccountingRecordIndex";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import {
  LedgerAccounts,
  AccountingClientSegment,
  AccountingEventType,
  AccountingEntry
} from "../../../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../../../configs/accountingConfig";
import { UserDocument } from "../../../models/User";
import Decimal from "decimal.js";

interface MissingEntryInfo {
  stage: string;
  entries: AccountingEntry[];
  description: string;
  articleDate: string;
}

interface DepositAnalysis {
  depositId: string;
  amount: number;
  userId: string;
  clientSegment: AccountingClientSegment;
  clientLedgerAccount: LedgerAccounts;
  isInstantFlow: boolean;
  currentStages: {
    stage1Completed: boolean;
    stage2Completed: boolean;
    stage3Completed: boolean;
  };
  existingLedgerEntries: any[];
  missingEntries: MissingEntryInfo[];
}

const fixDepositLedgerEntries = async (depositId: string, dryRun: boolean = true) => {
  try {
    console.info(`Starting ledger entry fix for deposit: ${depositId} (dry-run: ${dryRun})`);

    // 1. Fetch the deposit and populate necessary fields
    const deposit = await DepositCashTransaction.findById(depositId).populate([
      TransactionPopulationFieldsEnum.OWNER,
      TransactionPopulationFieldsEnum.CREDIT_TICKET
    ]);

    if (!deposit) {
      console.error(`Deposit with ID ${depositId} not found`);
      return;
    }

    if (deposit.consideration.currency !== "EUR") {
      console.error(`Deposit ${depositId} is not in EUR currency. Only EUR deposits are supported.`);
      return;
    }

    const user = deposit.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      console.error(`Deposit ${depositId} belongs to non-European entity. Only European entities are supported.`);
      return;
    }

    console.info(`Analyzing deposit: ${depositId}`);
    console.info(`- Amount: €${(deposit.consideration.amount / 100).toFixed(2)}`);
    console.info(`- User: ${user._id}`);
    console.info(`- Client Segment: ${user.accountingClientSegment}`);

    // 2. Analyze the deposit to determine what entries should exist
    const analysis = await analyzeDeposit(deposit, user);

    // 3. Check what ledger entries already exist
    const existingEntries = await AccountingLedgerStorageService.queryLedgerEntriesByTransactionId(depositId);
    analysis.existingLedgerEntries = existingEntries;

    console.info("\n=== DEPOSIT ANALYSIS ===");
    console.info(`Deposit Flow: ${analysis.isInstantFlow ? "Instant" : "Standard"}`);
    console.info("Current Stages:");
    console.info(`- Stage 1 (Devengo acquisition): ${analysis.currentStages.stage1Completed ? "✅" : "❌"}`);
    console.info(`- Stage 2 (Devengo collection): ${analysis.currentStages.stage2Completed ? "✅" : "❌"}`);
    console.info(`- Stage 3 (WealthKernel settled): ${analysis.currentStages.stage3Completed ? "✅" : "❌"}`);
    console.info(`Existing Ledger Entries: ${existingEntries.length}`);

    if (existingEntries.length > 0) {
      console.info("\n--- Existing Ledger Entries ---");
      existingEntries.forEach((entry, index) => {
        console.info(
          `${index + 1}. ${entry.account_code} ${entry.side} €${entry.amount.toFixed(2)} (AA: ${entry.aa})`
        );
      });
    }

    // 4. Determine missing entries
    const missingEntries = determineMissingEntries(analysis);

    if (missingEntries.length === 0) {
      console.info(`\n🎉 No missing ledger entries found for deposit ${depositId}`);
      return;
    }

    console.info("\n=== MISSING ENTRIES ANALYSIS ===");
    console.info(`Found ${missingEntries.length} missing entry group(s):`);

    missingEntries.forEach((missingEntry, index) => {
      console.info(`\n--- Missing Entry Group ${index + 1}: ${missingEntry.stage} ---`);
      console.info(`Description: ${missingEntry.description}`);
      console.info(`Article Date: ${missingEntry.articleDate}`);
      console.info("Entries:");
      missingEntry.entries.forEach((entry, entryIndex) => {
        console.info(`  ${entryIndex + 1}. ${entry.account} ${entry.type} €${(entry.amount / 100).toFixed(2)}`);
      });
    });

    // 5. Create entries if not in dry-run mode
    if (!dryRun) {
      console.info("\n=== CREATING MISSING ENTRIES ===");
      await createMissingLedgerEntries(depositId, missingEntries, user._id.toString());
      console.info(`✅ Successfully created ${missingEntries.length} missing entry group(s)`);
    } else {
      console.info("\n💡 This was a dry-run. To actually create the entries, run with --no-dry-run");
    }

    console.info(`\n🎯 Analysis completed for deposit ${depositId}\n`);
  } catch (error) {
    console.error("Error fixing deposit ledger entries:", { data: { error: error.toString() } });
  }
};

async function analyzeDeposit(deposit: any, user: UserDocument): Promise<DepositAnalysis> {
  const amount = deposit.consideration.amount;
  const clientSegment = user.accountingClientSegment;
  const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];
  const isInstantFlow = deposit.inInstantMoneyFlow;

  // Determine current stages completed
  const stage1Completed =
    deposit.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status === "confirmed";
  const stage2Completed =
    deposit.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed";
  const stage3Completed = deposit.providers?.wealthkernel?.status === "Settled";

  const missingEntries: MissingEntryInfo[] = [];

  // Generate expected entries for each completed stage
  if (stage1Completed) {
    const stage1Entries: AccountingEntry[] = [
      { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "debit" },
      { account: clientLedgerAccount, amount, type: "credit" }
    ];
    missingEntries.push({
      stage: "Stage 1 - Devengo Acquisition",
      entries: stage1Entries,
      description: getAccountingActivityDescription(
        user._id.toString(),
        deposit.id,
        AccountingEventType.BANK_TRANSACTION_DEPOSIT
      ),
      articleDate: new Date().toISOString().slice(0, 10)
    });
  }

  // Handle different flows correctly
  if (isInstantFlow) {
    // Instant Flow: 3 stages (includes Stage 2: Intermediary 1 → Intermediary 2)
    if (stage2Completed) {
      const stage2Entries: AccountingEntry[] = [
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
      ];
      missingEntries.push({
        stage: "Stage 2 - Devengo Collection (Instant Flow Only)",
        entries: stage2Entries,
        description: getAccountingActivityDescription(
          user._id.toString(),
          deposit.id,
          AccountingEventType.BANK_TRANSACTION_DEPOSIT
        ),
        articleDate: new Date().toISOString().slice(0, 10)
      });
    }

    if (stage3Completed) {
      // Instant Flow Stage 3: Intermediary 2 → Omnibus
      const stage3Entries: AccountingEntry[] = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "credit" }
      ];
      missingEntries.push({
        stage: "Stage 3 - WealthKernel Settlement (from Intermediary 2)",
        entries: stage3Entries,
        description: getAccountingActivityDescription(
          user._id.toString(),
          deposit.id,
          AccountingEventType.BANK_TRANSACTION_DEPOSIT
        ),
        articleDate: new Date().toISOString().slice(0, 10)
      });
    }
  } else {
    // Standard Flow: 2 stages only (Stage 1 and Stage 3, skipping Stage 2)
    if (stage3Completed) {
      // Standard Flow Stage 3: Intermediary 1 → Omnibus (directly, no Stage 2)
      const stage3Entries: AccountingEntry[] = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
      ];
      missingEntries.push({
        stage: "Stage 3 - WealthKernel Settlement (from Intermediary 1)",
        entries: stage3Entries,
        description: getAccountingActivityDescription(
          user._id.toString(),
          deposit.id,
          AccountingEventType.BANK_TRANSACTION_DEPOSIT
        ),
        articleDate: new Date().toISOString().slice(0, 10)
      });
    }
  }

  return {
    depositId: deposit.id,
    amount,
    userId: user._id.toString(),
    clientSegment,
    clientLedgerAccount,
    isInstantFlow,
    currentStages: {
      stage1Completed,
      stage2Completed,
      stage3Completed
    },
    existingLedgerEntries: [],
    missingEntries
  };
}

function determineMissingEntries(analysis: DepositAnalysis): MissingEntryInfo[] {
  const { missingEntries, existingLedgerEntries } = analysis;
  const actuallyMissingEntries: MissingEntryInfo[] = [];

  // For each expected entry group, check if it already exists in the ledger
  for (const expectedGroup of missingEntries) {
    // Check if all entries in this group already exist
    const allEntriesExist = expectedGroup.entries.every((expectedEntry) => {
      return existingLedgerEntries.some((existingEntry) => {
        const expectedAmountEuros = Decimal.div(expectedEntry.amount, 100).toNumber();
        const amountMatches = Math.abs(existingEntry.amount - expectedAmountEuros) < 0.01;
        const accountMatches = existingEntry.account_code === expectedEntry.account;
        const sideMatches = existingEntry.side === expectedEntry.type;

        return amountMatches && accountMatches && sideMatches;
      });
    });

    if (!allEntriesExist) {
      actuallyMissingEntries.push(expectedGroup);
    }
  }

  return actuallyMissingEntries;
}

async function createMissingLedgerEntries(
  depositId: string,
  missingEntries: MissingEntryInfo[],
  userId: string
): Promise<void> {
  for (const missingEntry of missingEntries) {
    console.info(`Creating entries for: ${missingEntry.stage}`);

    // Create AccountingRecordIndex for this entry group
    const recordIndex = await new AccountingRecordIndex({
      linkedDocumentId: depositId,
      sourceDocumentType: "Transaction"
    }).save();

    console.info(`Created AccountingRecordIndex with AA: ${recordIndex.aaIndex}`);

    // Transform entries to ledger format
    const ledgerEntries: AccountingLedgerEntry[] = missingEntry.entries.map((entry) => ({
      aa: recordIndex.aaIndex,
      account_code: entry.account,
      side: entry.type,
      amount: Decimal.div(entry.amount, 100).toNumber(), // Convert cents to euros
      reference_number: undefined as any,
      article_date: missingEntry.articleDate,
      description: missingEntry.description,
      document_id: depositId,
      owner_id: userId
    }));

    // Add to ledger
    const result = await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntries);

    if (result.success) {
      console.info(`✅ Successfully created ${ledgerEntries.length} ledger entries for ${missingEntry.stage}`);
    } else {
      console.error(`❌ Failed to create ledger entries for ${missingEntry.stage}: ${result.error}`);
    }
  }
}

function getAccountingActivityDescription(
  userId: string,
  transactionId: string,
  eventType: AccountingEventType
): string {
  return `${userId}|${transactionId}|${eventType}`;
}

// Main execution
(async () => {
  try {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      console.error("Usage: node fix-deposit-ledger-entries.ts <depositId> [--no-dry-run]");
      console.error("Example: node fix-deposit-ledger-entries.ts 507f1f77bcf86cd799439011");
      console.error("Example: node fix-deposit-ledger-entries.ts 507f1f77bcf86cd799439011 --no-dry-run");
      process.exit(1);
    }

    const depositId = args[0];
    const dryRun = !args.includes("--no-dry-run");

    if (!depositId.match(/^[0-9a-fA-F]{24}$/)) {
      console.error("Invalid deposit ID format. Must be a valid MongoDB ObjectId.");
      process.exit(1);
    }

    await fixDepositLedgerEntries(depositId, dryRun);
    process.exit(0);
  } catch (error) {
    console.error("Script execution failed:", error);
    process.exit(1);
  }
})();
