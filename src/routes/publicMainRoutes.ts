import express, { NextFunction, Request, Response } from "express";
import { CustomRequest } from "custom";
import path from "path";
import EventController from "../controllers/eventController";
import ParticipantController from "../controllers/participantController";
import logger from "../external-services/loggerService";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import MailchimpWebhookController from "../controllers/mailchimpWebhookController";
import TruelayerController from "../controllers/truelayerController";
import WealthkernelWebhookController from "../controllers/wealthkernelWebhookController";
import TruelayerWebhookController from "../controllers/truelayerWebhookController";
import StripeWebhookController from "../controllers/stripeWebhookController";
import { envIsDev } from "../utils/environmentUtil";
import ContentfulWebhookController from "../controllers/contentfulWebhookController";
import DevengoWebhookController from "../controllers/devengoWebhookController";
import GoCardlessDataController from "../controllers/goCardlessDataController";
import GoCardlessPaymentsWebhookController from "../controllers/goCardlessPaymentsWebhookController";
import SaltedgeWebhookController from "../controllers/saltedgeWebhookController";
import SumsubWebhookController from "../controllers/sumsubWebhookController";
import OneSignalWebhookController from "../controllers/onesignalWebhookController";
import { AssetDataController } from "../controllers/assetDataController";

const publicMainRoutes = express.Router();

publicMainRoutes.use((req: Request, res: Response, next: NextFunction) => {
  if (req.url.includes("/healthz")) {
    return next();
  }

  logger.info(`HTTP ${req.method} ${req.url}`, {
    data: {
      body: req.body,
      headers: req.headers
    }
  });
  return next();
});

publicMainRoutes.use("/healthz", (_: CustomRequest, res: Response) => {
  return res.sendStatus(204);
});

publicMainRoutes.use("/gocardless-data-callback", (_: CustomRequest, res: Response) => {
  return res.sendFile(path.join(__dirname + "/../../static/gocardless-data-callback.html"));
});

publicMainRoutes.use("/truelayer-data-callback", (_: CustomRequest, res: Response) => {
  return res.sendFile(path.join(__dirname + "/../../static/truelayer-data-callback.html"));
});

publicMainRoutes.use("/saltedge-pay-callback/:action", (_: CustomRequest, res: Response) => {
  return res.sendFile(path.join(__dirname + "/../../static/saltedge-pay-callback.html"));
});

/**
 * Path parameter action only used for url mapping.
 *
 */
publicMainRoutes.use("/truelayer-callback/:action", (_: CustomRequest, res: Response) => {
  return res.sendFile(path.join(__dirname + "/../../static/truelayer-pay-callback.html"));
});

/**
 * We serve the same apple-app-site-association under both .well-known and our root path.
 * @param _
 * @param res
 */
const getAppleAppSiteAssociation = (_: CustomRequest, res: Response) => {
  return res.status(200).json({
    applinks: {
      apps: [],
      details: [
        {
          appID: `${process.env.APPLE_APP_ID}`,
          paths: [
            "/truelayer-callback/*",
            "/saltedge-pay-callback/*",
            "/truelayer-data-callback/*",
            "/gocardless-data-callback/*"
          ]
        }
      ]
    }
  });
};

publicMainRoutes.use("/apple-app-site-association", getAppleAppSiteAssociation);
publicMainRoutes.use("/.well-known/apple-app-site-association", getAppleAppSiteAssociation);

publicMainRoutes.use("/.well-known/assetlinks.json", (_: CustomRequest, res: Response) => {
  return res.status(200).json([
    {
      relation: ["delegate_permission/common.handle_all_urls"],
      target: {
        namespace: "android_app",
        package_name: "com.wealthyhood.wealthyhood",
        sha256_cert_fingerprints: process.env.ANDROID_SHA_FINGERPRINT?.split(",")
      }
    }
  ]);
});

if (envIsDev()) {
  publicMainRoutes.use("/.well-known/jwks.json", (_: CustomRequest, res: Response) => {
    return res.sendFile(path.join(__dirname + "/../../static/jwks.json"));
  });
}

publicMainRoutes.post("/events", ErrorMiddleware.catchAsyncErrors(EventController.handleEvent));
publicMainRoutes.post("/logs", (req: Request, res: Response) => {
  const message = req.body.message as string;
  if (!message) {
    return;
  }

  const { platform, version } = req.headers;

  logger.info(message.substring(0, 50), {
    module: "publicMainRoutes",
    method: "logMessage",
    data: {
      appsflyerId: req.headers.appsflyer_id,
      attributionData: req.headers.appsflyer_attribution_data,
      message,
      platform,
      version
    }
  });
  return res.sendStatus(204);
});
publicMainRoutes.post("/participants", ErrorMiddleware.catchAsyncErrors(ParticipantController.createParticipant));

publicMainRoutes.get(
  "/asset/prices-by-tenor",
  ErrorMiddleware.catchAsyncErrors(AssetDataController.getAssetPricesByTenor)
);

publicMainRoutes.post(
  "/contentful/webhooks/publish",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(ContentfulWebhookController.processPublishWebhook)
);

publicMainRoutes.post(
  "/stripe/webhooks",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(StripeWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/saltedge/webhooks",
  ErrorMiddleware.catchAsyncErrors(SaltedgeWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/devengo/webhooks",
  ErrorMiddleware.catchAsyncErrors(DevengoWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/gocardless/webhooks",
  ErrorMiddleware.catchAsyncErrors(GoCardlessPaymentsWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/sumsub/webhooks",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(SumsubWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/mailchimp/webhooks",
  ErrorMiddleware.catchAsyncErrors(MailchimpWebhookController.processWebhook)
);

publicMainRoutes.post(
  "/truelayer/link-account",
  ErrorMiddleware.catchAsyncErrors(TruelayerController.linkAccount)
);

publicMainRoutes.post(
  "/gocardless/link-account",
  ErrorMiddleware.catchAsyncErrors(GoCardlessDataController.linkAccount)
);

publicMainRoutes.post(
  "/truelayer/webhooks/payments",
  ErrorMiddleware.catchAsyncErrors(TruelayerWebhookController.processPaymentsWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/bonuses",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processBonusWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/orders",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processOrderWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/accounts",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processAccountWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/deposits",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processDepositWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/withdrawals",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processWithdrawalWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/charges",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processChargeWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/webhooks/bank-accounts",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processBankAccountWebhook)
);

// Temporarily we have duplicated routes with and without region. After deploying these changes, we'll
// set up Wealthkernel to always use the routes **with** region.
publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/bonuses",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processBonusWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/orders",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processOrderWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/accounts",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processAccountWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/deposits",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processDepositWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/withdrawals",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processWithdrawalWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/charges",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processChargeWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/internal-transfers",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processInternalTransferWebhook)
);

publicMainRoutes.post(
  "/wealthkernel/:region/webhooks/bank-accounts",
  express.raw({ type: "application/json" }),
  ErrorMiddleware.catchAsyncErrors(WealthkernelWebhookController.processBankAccountWebhook)
);

publicMainRoutes.post(
  "/onesignal/webhooks/unsubscribe",
  ErrorMiddleware.catchAsyncErrors(OneSignalWebhookController.processUnsubscribeWebhook)
);

export default publicMainRoutes;
