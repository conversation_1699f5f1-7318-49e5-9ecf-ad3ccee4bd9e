import { custom<PERSON><PERSON><PERSON><PERSON> } from "nanoid";
import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../external-services/loggerService";
import { TransactionService } from "../services/transactionService";
import {
  DevengoAccountEventPayloadType,
  DevengoPaymentEventPayloadType,
  DevengoService
} from "../external-services/devengoService";
import DbUtil from "../utils/dbUtil";
import mongoose from "mongoose";
import PayoutService from "../services/payoutService";
import { BadRequestError } from "../models/ApiErrors";
import BankAccountService from "../services/bankAccountService";
import UserService from "../services/userService";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";
import BanksUtil from "../utils/banksUtil";
import { TransactionPopulationFieldsEnum } from "../models/Transaction";
import { TransferWithIntermediaryStageEnum } from "../types/transactions";
import { SaltedgeService } from "../external-services/saltedgeService";
import { BankAccountDocument } from "../models/BankAccount";
import { UserDocument } from "../models/User";
import { WalletRepository } from "../repositories/walletRepository";
import { TransactionRepository } from "../repositories/transactionRepository";
import { PayoutRepository } from "../repositories/payoutRepository";
import CreditTicketService from "../services/creditTicketService";

const BANK_REFERENCE_LENGTH = 10;
// These are the allowed characters in bank references
const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
const nanoid = customAlphabet(alphabet, BANK_REFERENCE_LENGTH);

/**
 * This controller handles webhooks received from Devengo.
 *
 * We're listening for 5 webhook events related to payments:
 * 1. incoming_payment.created: This happens when our Devengo account has received a payment from the outside world.
 * We can receive this event in four different accounts:
 *   a) A user wallet account -> this means the user has made a deposit using the bank details they saw on the
 *   app, and we are now just receiving the funds.
 *   b) Our bank transfer collection account -> this can happen in two cases:
 *     -> a deposit from (a) has been forwarded to our collection account.
 *     -> the user has created a withdrawal, and we are receiving the funds from Wealthkernel.
 *   c) Our GoCardless payout account -> this means we are receiving a payout from GoCardless. This is a batched
 *   payout that includes many user payments. We save the update in the payout document.
 *   d) Our Saltedge payments account We are receiving a payment from Saltedge.
 * 2. incoming_payment.confirmed: This happens when Devengo confirms the payment is completed and the funds are
 * available in our Devengo bank account. In this case, we save this update in the related document so that later,
 * (in ongoing cron), we can forward the necessary payments.
 *   a) When the payment is a bank transfer deposit, we will be forwarding the amount to our collection account.
 *   b) When the payment is a Saltedge deposit, we will be forwarding the amount to Wealthkernel.
 *   c) When the payment is a withdrawal, we will be forwarding the amount to the user's bank account.
 *   d) When the payment is a payout, we will be splitting it and forwarding the amount to Wealthkernel.
 * 3. incoming_payment.rejected: This happens when Devengo tell us that a payment was rejected. We don't need
 * to do anything apart from setting the relevant devengo payment status to rejected. The payment gets reversed
 * automatically by Devengo.
 * 4. outgoing_payment.confirmed: This happens when Devengo confirms an **outgoing payment** is completed and the
 * funds are available in the destination bank account.
 * 5. account.activated: This happens when Devengo activates an account. We use this to activate the user's wallet.
 *
 * More information on webhooks we're listening to can be found here:
 * https://docs.devengo.com/docs/webhooks-getting-started
 *
 */
class DevengoWebhookController {
  public static async processWebhook(req: CustomRequest, res: Response): Promise<Response> {
    DevengoService.validateWebhookSignature(req);

    const body = req.body as DevengoPaymentEventPayloadType | DevengoAccountEventPayloadType;

    logger.info(`Received event ${body.id} for type ${body.type} for entity ${body.data.object.id}`, {
      module: "DevengoWebhookController",
      method: "processWebhook",
      data: {
        body
      }
    });

    switch (body.type) {
      case "incoming_payment.created": {
        await DevengoWebhookController._handleIncomingPaymentCreated(body as DevengoPaymentEventPayloadType);
        break;
      }
      case "incoming_payment.confirmed": {
        await DevengoWebhookController._handleIncomingPaymentConfirmed(body as DevengoPaymentEventPayloadType);
        break;
      }
      case "incoming_payment.rejected": {
        await DevengoWebhookController._handleIncomingPaymentRejected(body as DevengoPaymentEventPayloadType);
        break;
      }
      case "outgoing_payment.confirmed": {
        await DevengoWebhookController._handleOutgoingPaymentConfirmed(body as DevengoPaymentEventPayloadType);
        break;
      }
      case "account.activated": {
        await DevengoWebhookController._handleAccountActivated(body as DevengoAccountEventPayloadType);
        break;
      }
      default: {
        logger.info(`Not processing events of type ${body.type}`, {
          module: "DevengoWebhookController",
          method: "processWebhook"
        });
        break;
      }
    }

    logger.info(`Successfully handled event ${body.id} for payment ${body.data.object.id} of type ${body.type}`, {
      module: "DevengoWebhookController",
      method: "processWebhook"
    });

    return res.sendStatus(200);
  }

  private static async _handleIncomingPaymentCreated(body: DevengoPaymentEventPayloadType): Promise<void> {
    const { third_party: thirdParty, description: reference, amount, account_id: accountId } = body.data.object;

    if (amount.currency !== "EUR") {
      throw new Error("Cannot accept payment for non-EUR currencies!");
    }

    const wallet = await WalletRepository.getWalletByDevengoId(accountId);

    if (accountId === process.env.DEVENGO_BANK_TRANSFER_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByBankReference(reference);

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming collection payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        {
          id: body.data.object.id,
          status: "created",
          accountId
        }
      );
    } else if (accountId === process.env.DEVENGO_PAYOUTS_ACCOUNT_ID) {
      const withdrawal =
        await TransactionRepository.getPendingWithdrawalWithIntermediaryByContainedBankReference(reference);

      if (!withdrawal) {
        throw new Error(`Could not find withdrawal for incoming collection payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateWithdrawalIncomingPaymentDevengoData(withdrawal._id.toString(), {
        id: body.data.object.id,
        status: "created",
        accountId
      });
    } else if (accountId === process.env.DEVENGO_SALTEDGE_COLLECTION_ACCOUNT_ID) {
      // Since we are receiving funds in our Saltedge collection account, the reference should match a Saltedge
      // custom payment ID.
      const deposit = await TransactionService.getDepositBySaltedgeCustomId(body.data.object.description);

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        const updatedDeposit = await TransactionService.updateDepositIncomingPaymentDevengoData(
          deposit.id,
          TransferWithIntermediaryStageEnum.COLLECTION,
          {
            id: body.data.object.id,
            status: "created",
            accountId
          },
          { session }
        );

        await DbUtil.populateIfNotAlreadyPopulated(updatedDeposit, TransactionPopulationFieldsEnum.BANK_ACCOUNT);
        const bankAccount = updatedDeposit.bankAccount as BankAccountDocument;

        // The IBAN that the amount is coming from.
        const iban = thirdParty.account.identifiers?.find((identifier) => identifier.type === "iban").iban;

        // We create a new bank account and update the deposit with its ID if:
        // 1. The user initiated the payment using a bank ID and not an existing bank account AND the IBAN was not
        // given to us by Saltedge at the time of payment completion so we're only receiving it now.
        // 2. The user initiated the payment using a bank account ID, BUT the debtor IBAN we're receiving now
        // is different to the one used.
        if (!bankAccount || bankAccount?.iban !== iban) {
          const payment = await SaltedgeService.Instance.getPayment(deposit?.providers?.saltedge?.id);

          await DbUtil.populateIfNotAlreadyPopulated(updatedDeposit, TransactionPopulationFieldsEnum.OWNER, {
            session
          });
          const user = updatedDeposit.owner as UserDocument;

          const newBankAccount = await BankAccountService.createBankAccount(
            {
              iban,
              name: user.fullName,
              active: true,
              owner: user.id,
              activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BANK_ACCOUNTS]),
              bankId: BanksUtil.getBankFromSaltedgeInstitutionId(payment.data.provider_code),
              currency: "EUR"
            },
            user
          );

          await TransactionService.addBankAccountToDeposit(updatedDeposit.id, newBankAccount.id, { session });
        }
      });
    } else if (accountId === process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID) {
      // Since we are receiving funds in our payouts collection account, the reference should match one of the
      // references in our payout collection.
      const payout = await PayoutRepository.getPendingPayoutByContainedReference(reference);

      if (!payout) {
        throw new BadRequestError(
          "Received Devengo webhook in our GoCardless payouts account but the payout reference does not match any of our payout documents!"
        );
      }

      await PayoutService.updatePayoutDevengoData(payout._id.toString(), {
        id: body.data.object.id,
        status: "created"
      });
    } else if (wallet) {
      const [deposit, user] = await Promise.all([
        TransactionService.getDepositByIncomingPaymentDevengoId(
          body.data.object.id,
          TransferWithIntermediaryStageEnum.ACQUISITION
        ),
        UserService.getUser(wallet.owner.toString())
      ]);

      if (deposit) {
        logger.warn(`Deposit already exists for incoming payment ${body.data.object.id}, returning!`, {
          module: "DevengoWebhookController",
          method: "_handleIncomingPaymentCreated",
          data: {
            body
          }
        });
        return;
      } else {
        // Neither a deposit nor a withdrawal has matched this payment, therefore it must be a new deposit.
        const iban = BanksUtil.getIBANFromDevengoThirdParty(thirdParty);

        let bankAccount = await BankAccountService.getBankAccountByIBAN(iban, user);
        if (!bankAccount) {
          bankAccount = await BankAccountService.createBankAccount(
            {
              owner: user.id,
              active: true,
              name: thirdParty.name,
              iban,
              bic: thirdParty.account.bank.bic,
              bankName: thirdParty.account.bank.name,
              bankId: BanksUtil.getBankFromBIC(thirdParty.account.bank.bic),
              activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BANK_ACCOUNTS]),
              currency: "EUR"
            },
            user
          );
        }

        await TransactionService.createBankTransferDepositTransaction(user, {
          amount: amount.cents,
          // We set a unique bank reference on the deposit, ignoring what the user has provided as a description,
          // to ensure the uniqueness of the bank references and our ability to then identify the deposit by reference.
          bankReference: nanoid(),
          bankAccountId: bankAccount.id,
          devengo: {
            id: body.data.object.id,
            // We set the status to created here, even though the webhook data will sometimes have it defined as confirmed immediately.
            // Doing this allows for the correct handling of payment confirmation by the next webhook Devengo sends, payment.confirmed.
            status: "created",
            accountId: body.data.object.account_id
          }
        });
      }
    } else {
      throw new BadRequestError(
        `Received Devengo webhook but account ID ${accountId} does not match any of our known accounts`
      );
    }
  }

  private static async _handleIncomingPaymentConfirmed(body: DevengoPaymentEventPayloadType): Promise<void> {
    const { account_id: accountId, description: incomingReference } = body.data.object;

    const wallet = await WalletRepository.getWalletByDevengoId(accountId);

    if (accountId === process.env.DEVENGO_BANK_TRANSFER_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        {
          id: body.data.object.id,
          status: "confirmed",
          accountId
        }
      );
    } else if (accountId === process.env.DEVENGO_PAYOUTS_ACCOUNT_ID) {
      const withdrawal = await TransactionService.getWithdrawalByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!withdrawal) {
        throw new Error(`Could not find withdrawal for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateWithdrawalIncomingPaymentDevengoData(withdrawal.id, {
        id: body.data.object.id,
        status: "confirmed",
        accountId
      });
    } else if (accountId === process.env.DEVENGO_SALTEDGE_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find Saltedge deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        {
          id: body.data.object.id,
          status: "confirmed",
          accountId
        }
      );
    } else if (accountId === process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID) {
      const payout = await PayoutRepository.getPendingPayoutByContainedReference(incomingReference);

      if (!payout) {
        throw new BadRequestError(
          "Received Devengo webhook in our GoCardless payouts account but the payout reference does not match any of our payout documents!"
        );
      }

      await PayoutService.updatePayoutDevengoData(payout._id.toString(), {
        id: body.data.object.id,
        status: "confirmed"
      });
    } else if (wallet) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.ACQUISITION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        const updatedDeposit = await TransactionService.updateDepositIncomingPaymentDevengoData(
          deposit.id,
          TransferWithIntermediaryStageEnum.ACQUISITION,
          {
            id: body.data.object.id,
            status: "confirmed",
            accountId
          },
          { session }
        );

        await CreditTicketService.createCreditTicketForDeposit(updatedDeposit, {
          session,
          requestInstantly: true
        });

        await TransactionService.emitDepositCreatedEvent(updatedDeposit, { triggeredByAutomation: false });
      });
    } else {
      throw new BadRequestError(
        `Received Devengo webhook but account ID ${accountId} does not match any of our known accounts`
      );
    }
  }

  private static async _handleIncomingPaymentRejected(body: DevengoPaymentEventPayloadType): Promise<void> {
    const { account_id: accountId, description: incomingReference } = body.data.object;

    const wallet = await WalletRepository.getWalletByDevengoId(accountId);

    if (accountId === process.env.DEVENGO_BANK_TRANSFER_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        {
          id: body.data.object.id,
          status: "rejected",
          accountId
        }
      );
    } else if (accountId === process.env.DEVENGO_PAYOUTS_ACCOUNT_ID) {
      const withdrawal = await TransactionService.getWithdrawalByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!withdrawal) {
        throw new Error(`Could not find withdrawal for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateWithdrawalIncomingPaymentDevengoData(withdrawal.id, {
        id: body.data.object.id,
        status: "rejected",
        accountId
      });
    } else if (accountId === process.env.DEVENGO_SALTEDGE_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find Saltedge deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        {
          id: body.data.object.id,
          status: "rejected",
          accountId
        }
      );
    } else if (accountId === process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID) {
      const payout = await PayoutRepository.getPendingPayoutByContainedReference(incomingReference);

      if (!payout) {
        throw new BadRequestError(
          "Received Devengo webhook in our GoCardless payouts account but the payout reference does not match any of our payout documents!"
        );
      }

      await PayoutService.updatePayoutDevengoData(payout._id.toString(), {
        id: body.data.object.id,
        status: "rejected"
      });
    } else if (wallet) {
      const deposit = await TransactionService.getDepositByIncomingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.ACQUISITION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositIncomingPaymentDevengoData(
        deposit.id,
        TransferWithIntermediaryStageEnum.ACQUISITION,
        {
          id: body.data.object.id,
          status: "rejected",
          accountId
        }
      );
    } else {
      throw new BadRequestError(
        `Received Devengo webhook but account ID ${accountId} does not match any of our known accounts`
      );
    }
  }

  private static async _handleOutgoingPaymentConfirmed(body: DevengoPaymentEventPayloadType): Promise<void> {
    const { account_id: accountId } = body.data.object;

    const wallet = await WalletRepository.getWalletByDevengoId(accountId);

    if (accountId === process.env.DEVENGO_BANK_TRANSFER_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByOutgoingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for outgoing payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositOutgoingPaymentDevengoData(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        "confirmed"
      );
    } else if (accountId === process.env.DEVENGO_PAYOUTS_ACCOUNT_ID) {
      const withdrawal = await TransactionService.getWithdrawalByOutgoingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!withdrawal) {
        throw new Error(`Could not find withdrawal for outgoing payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateWithdrawalOutgoingPaymentDevengoData(withdrawal.id, {
        id: body.data.object.id,
        status: "confirmed"
      });
    } else if (accountId === process.env.DEVENGO_SALTEDGE_COLLECTION_ACCOUNT_ID) {
      const deposit = await TransactionService.getDepositByOutgoingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION
      );

      if (!deposit) {
        throw new Error(`Could not find Saltedge deposit for outgoing payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositOutgoingPaymentDevengoData(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.COLLECTION,
        "confirmed"
      );
    } else if (wallet) {
      const deposit = await TransactionService.getDepositByOutgoingPaymentDevengoId(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.ACQUISITION
      );

      if (!deposit) {
        throw new Error(`Could not find deposit for incoming payment ID ${body.data.object.id}!`);
      }

      await TransactionService.updateDepositOutgoingPaymentDevengoData(
        body.data.object.id,
        TransferWithIntermediaryStageEnum.ACQUISITION,
        "confirmed"
      );
    }
  }

  private static async _handleAccountActivated(body: DevengoAccountEventPayloadType): Promise<void> {
    const { id: accountId, status, identifiers } = body.data.object;

    const wallet = await WalletRepository.getWalletByDevengoId(accountId);
    if (!wallet) {
      throw new Error(`Could not find wallet for activated account ID ${accountId}!`);
    }

    await WalletRepository.updateWalletDevengoData(wallet.id, {
      devengo: { id: accountId, status },
      identifiers
    });
  }
}

export default DevengoWebhookController;
