import { Request, Response } from "express";
import {
  mailchimpWebhookEvents,
  MailchimpWebhookNotificationEventType
} from "../event-handlers/notificationEvents";
import logger from "../external-services/loggerService";
import { User } from "../models/User";
import NotificationService from "../services/notificationService";

interface MailchimpWebhookProperties {
  [key: string]: any;
}

export default class MailchimpWebhookController {
  public static async processWebhook(req: Request, res: Response): Promise<Response> {
    const body = req.body;
    const headers = req.headers;

    const { notification, email, properties } = body as {
      notification: MailchimpWebhookNotificationEventType;
      email: string;
      properties: MailchimpWebhookProperties;
    };

    logger.info(`Received mailchimp webhook for notification ${notification} & user ${email}`, {
      module: "MailchimpWebhookController",
      method: "processWebhook",
      data: {
        body,
        headers
      }
    });

    if (!mailchimpWebhookEvents.includes(notification)) {
      logger.error(`Received mailchimp webhook for invalid notification ${notification}`, {
        module: "MailchimpWebhookController",
        method: "processWebhook",
        data: {
          body,
          headers
        }
      });
      return res.sendStatus(403);
    }

    const user = await User.findOne({ email });

    if (!user) {
      logger.error(`Received mailchimp webhook for email ${email} but could not retrieve user`, {
        module: "MailchimpWebhookController",
        method: "processWebhook",
        data: {
          body,
          headers
        }
      });
      return res.sendStatus(403);
    }

    await NotificationService.createAppNotification(
      user.id,
      { notificationId: notification, properties: new Map(Object.entries(properties ?? {})) },
      { sendImmediately: true }
    );

    return res.sendStatus(204);
  }
}
