import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import {
  DepositCashTransaction,
  WithdrawalCashTransaction,
  DividendTransaction,
  SavingsDividendTransaction
} from "../models/Transaction";
import { Order } from "../models/Order";
import { Reward } from "../models/Reward";
import { Gift } from "../models/Gift";
import { CreditTicket } from "../models/CreditTicket";
import AccountingLedgerStorageService, {
  LedgerQueryResult
} from "../external-services/accountingLedgerStorageService";
import { AccountingEventType, LedgerAccounts } from "../types/accounting";
import { CASH_ACCOUNTS_WK_MAPPING } from "../configs/accountingConfig";
import logger from "../external-services/loggerService";

// Transaction IDs to ignore in validation (known issues or exceptions)
const IGNORED_DEPOSIT_IDS = [
  "68669c975e76e853cccc2069",
  "6866d70e69c5d137054c1f71",
  "68684d7dedf0cca4df7872cb",
  "688b5e9680f8c2b3011c717c",
  "688c62b0bdec37c7190c7c6d",
  "688d4413cde297b72fdbff39",

  // The following where failing in stage 2 validation due to having
  // intermediary 2 -> client accounts instead of intermediary 1 -> client accounts
  // This was probably due to manually changing the status of the documents.
  "68833c33dea8e360e7954c40",
  "688dae0ccde297b72fdcb095",
  "6894a0eed7aff47877ab35ca"
];

export interface ValidationResult {
  transactionType:
    | "deposits_stage1"
    | "deposits_stage2"
    | "deposits_stage3"
    | "withdrawals_stage1"
    | "withdrawals_stage2"
    | "dividends_asset"
    | "dividends_mmf_receipt"
    | "dividends_mmf_commission"
    | "asset_buy"
    | "asset_sell"
    | "mmf_buy"
    | "mmf_sell"
    | "mmf_internally_filled"
    | "rewards_deposit"
    | "rewards_order"
    | "gifts_deposit"
    | "deposits_direct_debit_stage1"
    | "deposits_direct_debit_stage2";
  isValid: boolean;
  dbTotalAmount: number; // in euros
  ledgerTotalAmount: number; // in euros
  difference: number; // in euros
  transactionCount: number;
  ledgerEntryCount: number;
  discrepancies?: ValidationDiscrepancy[];
}

export interface ValidationDiscrepancy {
  transactionId: string;
  dbAmount: number; // in euros
  ledgerAmount: number; // in euros
  difference: number; // in euros
  description: string; // describes the specific issue
}

interface CashReconDelta {
  deltaWK: number;
  deltaLedger: number;
  difference: number;
}

export interface CashReconciliationResult {
  fromDate: string;
  perAccount: Record<LedgerAccounts, CashReconDelta>;
  aggregate: CashReconDelta;
}

export class AccountingValidationService {
  /**
   * Validates deposits, withdrawals, dividends, orders, rewards, and gifts
   */
  public static async validateAllDbWithLedger(fromDate: string): Promise<{
    deposits: ValidationResult[];
    withdrawals: ValidationResult[];
    dividends: ValidationResult[];
    orders: ValidationResult[];
    rewards: ValidationResult[];
    gifts: ValidationResult[];
  }> {
    logger.info("Starting all validation", {
      module: "AccountingValidationService",
      method: "validateAllDbWithLedger",
      data: { fromDate }
    });

    const [depositsResults, withdrawalsResults, dividendsResults, ordersResults, rewardsResults, giftsResults] =
      await Promise.all([
        AccountingValidationService.validateDepositsDbWithLedger(fromDate),
        AccountingValidationService.validateWithdrawalsDbWithLedger(fromDate),
        AccountingValidationService.validateDividendsDbWithLedger(fromDate),
        AccountingValidationService.validateOrdersDbWithLedger(fromDate),
        AccountingValidationService.validateRewardsDbWithLedger(fromDate),
        AccountingValidationService.validateGiftsDbWithLedger(fromDate)
      ]);

    logger.info("All validation completed", {
      module: "AccountingValidationService",
      method: "validateAllDbWithLedger",
      data: { depositsResults, withdrawalsResults, dividendsResults, ordersResults, rewardsResults, giftsResults }
    });

    return {
      deposits: depositsResults,
      withdrawals: withdrawalsResults,
      dividends: dividendsResults,
      orders: ordersResults,
      rewards: rewardsResults,
      gifts: giftsResults
    };
  }

  /**
   * Validates that deposit transaction amounts match their accounting ledger entries
   *
   * Checks for Stage 1 (External → Intermediary #1)
   * => Total db amount should match total credits on client accounts
   * => Total db amount should match total debits on intermediary deposits #1 account
   *
   * Checks for Stage 2 (Intermediary #1 → Intermediary #2) - Instant flow only
   * => Total db amount should match total credits on intermediary deposits #1 account
   * => Total db amount should match total debits on intermediary deposits #2 account
   *
   * Checks for Stage 3 (Intermediary → Omnibus)
   * => Total db amount should match total credits on intermediary deposits account (stage depends on flow type)
   * => Total db amount should match total debits on omnibus account
   */
  public static async validateDepositsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting deposits validation", {
      module: "AccountingValidationService",
      method: "validateDepositsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bank transactions with date filter
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BANK_TRANSACTION_DEPOSIT,
        fromDate
      );

      // Stage 1 Validation: Devengo acquisition confirmed deposits (External → Intermediary #1)
      const stage1Query: any = {
        "consideration.currency": "EUR",
        "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.acquisition.incomingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        },
        _id: { $nin: IGNORED_DEPOSIT_IDS }
      };

      const stage1Deposits = await DepositCashTransaction.find(stage1Query).populate("owner");

      const stage1DbTotalAmount = stage1Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage1DepositIds = new Set(stage1Deposits.map((d) => d._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check credits on client accounts
      const stage1CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on intermediary deposits #1 account
      const stage1DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage1CreditDifference = new Decimal(stage1DbTotalAmount).minus(stage1CreditAmount).toNumber();
      const stage1DebitDifference = new Decimal(stage1DbTotalAmount).minus(stage1DebitAmount).toNumber();
      const stage1IsValid = stage1CreditDifference === 0 && stage1DebitDifference === 0;

      const stage1Result: ValidationResult = {
        transactionType: "deposits_stage1",
        isValid: stage1IsValid,
        dbTotalAmount: stage1DbTotalAmount,
        ledgerTotalAmount: stage1CreditAmount,
        difference: stage1CreditDifference,
        transactionCount: stage1Deposits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "debit") ||
              (clientAccountCodes.has(entry.account_code as LedgerAccounts) && entry.side === "credit"))
          );
        }).length
      };

      if (!stage1IsValid) {
        stage1Result.discrepancies = await AccountingValidationService._findDepositDiscrepancies(
          stage1Deposits,
          ledgerEntries
        );
      }

      // Stage 2 Validation: Devengo collection confirmed deposits (Intermediary #1 → Intermediary #2) - Instant flow only
      const stage2DepositsRaw = await DepositCashTransaction.find({
        "consideration.currency": "EUR",
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        },
        _id: { $nin: IGNORED_DEPOSIT_IDS }
      }).populate(["owner", "linkedCreditTicket"]);

      // Filter for instant flow deposits only - using explicit CreditTicket lookup instead of virtual property
      const stage2Deposits = [];
      for (const deposit of stage2DepositsRaw) {
        if (deposit.inInstantMoneyFlow) {
          stage2Deposits.push(deposit);
        }
      }

      const stage2DbTotalAmount = stage2Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage2DepositIds = new Set(stage2Deposits.map((d) => d._id.toString()));

      // Check credits on intermediary deposits #1 account
      // Stage 2 entries should be paired: credit to INTERMEDIARY_DEPOSITS_1 + debit to INTERMEDIARY_DEPOSITS_2 with same AA
      const stage2CreditEntries = ledgerEntries.filter((entry) => {
        const transactionId = entry.document_id;
        if (
          !stage2DepositIds.has(transactionId) ||
          entry.side !== "credit" ||
          entry.account_code !== LedgerAccounts.INTERMEDIARY_DEPOSITS_1
        ) {
          return false;
        }

        // Only count this credit entry if there's a matching debit to INTERMEDIARY_DEPOSITS_2 with same AA
        const matchingDebit = ledgerEntries.find(
          (debitEntry) =>
            debitEntry.document_id === transactionId &&
            debitEntry.side === "debit" &&
            debitEntry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2 &&
            debitEntry.aa === entry.aa
        );

        return !!matchingDebit;
      });

      const stage2CreditAmount = stage2CreditEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on intermediary deposits #2 account
      // Only count debits that are paired with credits to INTERMEDIARY_DEPOSITS_1 with same AA
      const stage2DebitEntries = ledgerEntries.filter((entry) => {
        const transactionId = entry.document_id;
        if (
          !stage2DepositIds.has(transactionId) ||
          entry.side !== "debit" ||
          entry.account_code !== LedgerAccounts.INTERMEDIARY_DEPOSITS_2
        ) {
          return false;
        }

        // Only count this debit entry if there's a matching credit to INTERMEDIARY_DEPOSITS_1 with same AA
        const matchingCredit = ledgerEntries.find(
          (creditEntry) =>
            creditEntry.document_id === transactionId &&
            creditEntry.side === "credit" &&
            creditEntry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 &&
            creditEntry.aa === entry.aa
        );

        return !!matchingCredit;
      });

      const stage2DebitAmount = stage2DebitEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage2CreditDifference = new Decimal(stage2DbTotalAmount).minus(stage2CreditAmount).toNumber();
      const stage2DebitDifference = new Decimal(stage2DbTotalAmount).minus(stage2DebitAmount).toNumber();
      const stage2IsValid = stage2CreditDifference === 0 && stage2DebitDifference === 0;

      const stage2Result: ValidationResult = {
        transactionType: "deposits_stage2",
        isValid: stage2IsValid,
        dbTotalAmount: stage2DbTotalAmount,
        ledgerTotalAmount: stage2CreditAmount,
        difference: stage2CreditDifference,
        transactionCount: stage2Deposits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2 && entry.side === "debit") ||
              (entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "credit"))
          );
        }).length
      };

      if (!stage2IsValid) {
        stage2Result.discrepancies = await AccountingValidationService._findDepositStage2Discrepancies(
          stage2Deposits,
          ledgerEntries
        );
      }

      // Stage 3 Validation: WealthKernel settled deposits (Intermediary → Omnibus)
      const stage3Query: any = {
        "consideration.currency": "EUR",
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": { $gte: new Date(fromDate) },
        _id: { $nin: IGNORED_DEPOSIT_IDS }
      };

      const stage3Deposits = await DepositCashTransaction.find(stage3Query).populate([
        "owner",
        "linkedCreditTicket"
      ]);

      const stage3DbTotalAmount = stage3Deposits
        .reduce((sum, deposit) => {
          const amount = Decimal.div(deposit.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage3DepositIds = new Set(stage3Deposits.map((d) => d._id.toString()));

      // NEW: Flow-aware validation - check each deposit individually for correct intermediary account
      let stage3CreditAmount = 0;
      let stage3DebitAmount = 0;
      let stage3FlowMismatches = 0;

      for (const deposit of stage3Deposits) {
        const transactionId = deposit._id.toString();

        const expectedCreditAccount = deposit.inInstantMoneyFlow
          ? LedgerAccounts.INTERMEDIARY_DEPOSITS_2
          : LedgerAccounts.INTERMEDIARY_DEPOSITS_1;

        // Find credit entries for this transaction using the CORRECT intermediary account
        const creditEntries = ledgerEntries.filter((credit) => {
          return (
            credit.document_id === transactionId &&
            credit.side === "credit" &&
            credit.account_code === expectedCreditAccount
          );
        });

        // Find debit entries for this transaction
        const debitEntries = ledgerEntries.filter((debit) => {
          return (
            debit.document_id === transactionId &&
            debit.side === "debit" &&
            debit.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        });

        // Check for WRONG intermediary account usage - but only for Stage 3 entries
        // (entries that are paired with CLIENTS_ACCOUNTS_OMNIBUS debits)
        const wrongCreditAccount = deposit.inInstantMoneyFlow
          ? LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          : LedgerAccounts.INTERMEDIARY_DEPOSITS_2;

        const wrongCreditEntries = ledgerEntries.filter((credit) => {
          if (
            credit.document_id !== transactionId ||
            credit.side !== "credit" ||
            credit.account_code !== wrongCreditAccount
          ) {
            return false;
          }

          // Only count this as a "wrong Stage 3 entry" if it's paired with an omnibus debit (same AA)
          return ledgerEntries.some(
            (debit) =>
              debit.aa === credit.aa &&
              debit.side === "debit" &&
              debit.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS &&
              debit.document_id === transactionId
          );
        });

        if (wrongCreditEntries.length > 0) {
          stage3FlowMismatches++;
        }

        // Sum up the amounts
        // Use Decimal for all numerical calculations to avoid floating point precision issues.
        const creditSum = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0));
        const debitSum = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0));

        stage3CreditAmount = new Decimal(stage3CreditAmount).plus(creditSum).toNumber();
        stage3DebitAmount = new Decimal(stage3DebitAmount).plus(debitSum).toNumber();
      }

      // Both debit and credit sides should match db total AND no flow mismatches
      const stage3CreditDifference = new Decimal(stage3DbTotalAmount).minus(stage3CreditAmount).toNumber();
      const stage3DebitDifference = new Decimal(stage3DbTotalAmount).minus(stage3DebitAmount).toNumber();
      const stage3IsValid =
        stage3CreditDifference === 0 && stage3DebitDifference === 0 && stage3FlowMismatches === 0;

      const stage3Result: ValidationResult = {
        transactionType: "deposits_stage3",
        isValid: stage3IsValid,
        dbTotalAmount: stage3DbTotalAmount,
        ledgerTotalAmount: stage3CreditAmount,
        difference: stage3CreditDifference,
        transactionCount: stage3Deposits.length,
        // Count ledger entries that match the flow-aware validation logic
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          if (!stage3DepositIds.has(transactionId)) return false;

          // Find the deposit to determine its flow type
          const deposit = stage3Deposits.find((d) => d._id.toString() === transactionId);
          if (!deposit) return false;

          // Determine expected intermediary account for this deposit
          let isInstantFlow = false;
          if (deposit.linkedCreditTicket) {
            // Note: This is synchronous approximation - in practice the CreditTicket lookup was done above
            isInstantFlow = !!deposit.linkedCreditTicket; // Simplified for counting
          }

          const expectedCreditAccount = isInstantFlow
            ? LedgerAccounts.INTERMEDIARY_DEPOSITS_2
            : LedgerAccounts.INTERMEDIARY_DEPOSITS_1;

          return (
            (entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit") ||
            (entry.account_code === expectedCreditAccount && entry.side === "credit")
          );
        }).length
      };

      if (!stage3IsValid) {
        stage3Result.discrepancies = await AccountingValidationService._findDepositStage3Discrepancies(
          stage3Deposits,
          ledgerEntries
        );
      }

      logger.info("Deposits validation completed", {
        module: "AccountingValidationService",
        method: "validateDepositsDbWithLedger",
        data: { stage1Result, stage2Result, stage3Result }
      });

      // Direct Debit Validation
      const directDebitResults = await AccountingValidationService._validateDirectDebitDeposits(
        fromDate,
        ledgerEntries
      );

      logger.info("Deposits validation completed", {
        module: "AccountingValidationService",
        method: "validateDepositsDbWithLedger",
        data: { stage1Result, stage2Result, stage3Result, directDebitResults }
      });

      return [stage1Result, stage2Result, stage3Result, ...directDebitResults];
    } catch (error) {
      captureException(error);
      logger.error("Error during deposits validation", {
        module: "AccountingValidationService",
        method: "validateDepositsDbWithLedger",
        data: { error }
      });
    }
  }

  /**
   * Validates that withdrawal transaction amounts match their accounting ledger entries
   * Validates both Stage 1 (WealthKernel settled) and Stage 2 (Devengo outgoing confirmed)
   *
   * Checks for Stage 1 (Omnibus → Intermediary)
   * => Total db amount should match total credits on omnibus account
   * => Total db amount should match total debits on intermediary withdrawals account
   *
   * Checks for Stage 2 (Intermediary → Client)
   * => Total db amount should match total credits on intermediary withdrawals account
   * => Total db amount should match total debits on client accounts
   */
  public static async validateWithdrawalsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting withdrawals validation", {
      module: "AccountingValidationService",
      method: "validateWithdrawalsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bank transactions with date filter
      // TODO: update how we fetch that
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BANK_TRANSACTION_WITHDRAWAL,
        fromDate
      );

      // Stage 1 Validation: WealthKernel settled withdrawals (Omnibus → Intermediary)
      const stage1Withdrawals = await WithdrawalCashTransaction.find({
        "consideration.currency": "EUR",
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": { $gte: new Date(fromDate) }
      }).populate("owner");

      const stage1DbTotalAmount = stage1Withdrawals
        .reduce((sum, withdrawal) => {
          const amount = Decimal.div(withdrawal.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage1WithdrawalIds = new Set(stage1Withdrawals.map((w) => w._id.toString()));

      // Check debits on intermediary withdrawals account
      const stage1DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1WithdrawalIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits on omnibus account
      const stage1CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1WithdrawalIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage1DebitDifference = new Decimal(stage1DbTotalAmount).minus(stage1DebitAmount).toNumber();
      const stage1CreditDifference = new Decimal(stage1DbTotalAmount).minus(stage1CreditAmount).toNumber();
      const stage1IsValid = stage1DebitDifference === 0 && stage1CreditDifference === 0;

      const stage1Result: ValidationResult = {
        transactionType: "withdrawals_stage1",
        isValid: stage1IsValid,
        dbTotalAmount: stage1DbTotalAmount,
        ledgerTotalAmount: stage1CreditAmount,
        difference: stage1CreditDifference,
        transactionCount: stage1Withdrawals.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return stage1WithdrawalIds.has(transactionId);
        }).length
      };

      if (!stage1IsValid) {
        stage1Result.discrepancies = await AccountingValidationService._findWithdrawalDiscrepancies(
          stage1Withdrawals,
          ledgerEntries
        );
      }

      // Stage 2 Validation: Devengo outgoing confirmed withdrawals (Intermediary → Client)
      const stage2Withdrawals = await WithdrawalCashTransaction.find({
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": "confirmed",
        "transferWithIntermediary.collection.outgoingPayment.providers.devengo.settledAt": {
          $gte: new Date(fromDate)
        }
      }).populate("owner");

      const stage2DbTotalAmount = stage2Withdrawals
        .reduce((sum, withdrawal) => {
          const amount = Decimal.div(withdrawal.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage2WithdrawalIds = new Set(stage2Withdrawals.map((w) => w._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check debits on client accounts
      const stage2DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2WithdrawalIds.has(transactionId) &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits on intermediary withdrawals account
      const stage2CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2WithdrawalIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const stage2DebitDifference = new Decimal(stage2DbTotalAmount).minus(stage2DebitAmount).toNumber();
      const stage2CreditDifference = new Decimal(stage2DbTotalAmount).minus(stage2CreditAmount).toNumber();
      const stage2IsValid = stage2DebitDifference === 0 && stage2CreditDifference === 0;

      const stage2Result: ValidationResult = {
        transactionType: "withdrawals_stage2",
        isValid: stage2IsValid,
        dbTotalAmount: stage2DbTotalAmount,
        ledgerTotalAmount: stage2DebitAmount,
        difference: stage2DebitDifference,
        transactionCount: stage2Withdrawals.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return stage2WithdrawalIds.has(transactionId);
        }).length
      };

      if (!stage2IsValid) {
        stage2Result.discrepancies = await AccountingValidationService._findWithdrawalDiscrepancies(
          stage2Withdrawals,
          ledgerEntries
        );
      }

      logger.info("Withdrawals validation completed", {
        module: "AccountingValidationService",
        method: "validateWithdrawalsDbWithLedger",
        data: { stage1Result, stage2Result }
      });

      return [stage1Result, stage2Result];
    } catch (error) {
      captureException(error);
      logger.error("Error during withdrawals validation", {
        module: "AccountingValidationService",
        method: "validateWithdrawalsDbWithLedger",
        data: { error }
      });
    }
  }

  /**
   * Validates that dividend transaction amounts match their accounting ledger entries
   * Validates three types of dividend flows according to PRD_ACCOUNTING.md specifications
   *
   * Checks for Asset Dividends (DividendTransaction)
   * => Validates that total dividend amounts debited from omnibus account
   * => match total dividend amounts credited to client accounts
   * => Uses ASSET_DIVIDEND event type for ledger entry matching
   *
   * Checks for MMF Dividend Receipt (SavingsDividendTransaction - Receipt flow)
   * => Validates gross dividend flow from omnibus to client accounts
   * => Debits: CLIENTS_ACCOUNTS_OMNIBUS for gross dividend amount
   * => Credits: Client accounts (CLIENT_DOMESTIC/CLIENT_EU_EEA/CLIENT_INTERNATIONAL) for gross dividend amount
   * => Uses ASSET_DIVIDEND event type for ledger entry matching
   *
   * Checks for MMF Dividend Commission (SavingsDividendTransaction - Commission flow)
   * => Validates commission charges from client accounts to fee income account
   * => Debits: Client accounts for commission amount
   * => Credits: MMF_DIVIDEND_FEES_WH account for commission amount
   * => Uses MMF_DIVIDEND_COMMISSION event type for ledger entry matching
   *
   * @param fromDate - ISO date string to filter transactions from this date onwards
   * @returns Promise<ValidationResult[]> - Array with 3 validation results (asset, mmf_receipt, mmf_commission)
   */
  public static async validateDividendsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting dividends validation", {
      module: "AccountingValidationService",
      method: "validateDividendsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for dividend transactions with date filter
      const assetDividendLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_DIVIDEND,
        fromDate
      );

      const mmfCommissionLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.MMF_DIVIDEND_COMMISSION,
        fromDate
      );

      // Asset Dividends Validation (DividendTransaction)
      const assetDividends = await DividendTransaction.find({
        "consideration.currency": "EUR",
        $or: [{ "providers.wealthkernel.status": "Settled" }, { "providers.wealthkernel.status": "Matched" }],
        settledAt: { $gte: new Date(fromDate) }
      }).populate("owner");

      const assetDbTotalAmount = assetDividends
        .reduce((sum, dividend) => {
          const amount = new Decimal(dividend.consideration.amount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const assetDividendIds = new Set(assetDividends.map((d) => d._id.toString()));
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Check credits to client accounts for asset dividends
      const assetCreditAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            assetDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits from omnibus account for asset dividends
      const assetDebitAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            assetDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const assetCreditDifference = new Decimal(assetDbTotalAmount).minus(assetCreditAmount).toNumber();
      const assetDebitDifference = new Decimal(assetDbTotalAmount).minus(assetDebitAmount).toNumber();
      const assetIsValid = assetCreditDifference === 0 && assetDebitDifference === 0;

      const assetDividendsResult: ValidationResult = {
        transactionType: "dividends_asset",
        isValid: assetIsValid,
        dbTotalAmount: assetDbTotalAmount,
        ledgerTotalAmount: assetCreditAmount,
        difference: assetCreditDifference,
        transactionCount: assetDividends.length,
        ledgerEntryCount: assetDividendLedgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return assetDividendIds.has(transactionId);
        }).length
      };

      if (!assetIsValid) {
        assetDividendsResult.discrepancies = await AccountingValidationService._findAssetDividendDiscrepancies(
          assetDividends,
          assetDividendLedgerEntries
        );
      }

      // MMF Dividend Receipt Validation (SavingsDividendTransaction - Receipt flow)
      const mmfDividends = await SavingsDividendTransaction.find({
        "consideration.currency": "EUR",
        createdAt: { $gte: new Date(fromDate) }
      }).populate("owner");

      const mmfReceiptDbTotalAmount = mmfDividends
        .reduce((sum, dividend) => {
          const amount = new Decimal(dividend.originalDividendAmount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const mmfDividendIds = new Set(mmfDividends.map((d) => d._id.toString()));

      // Check credits to client accounts for MMF dividend receipts
      const mmfReceiptCreditAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits from omnibus account for MMF dividend receipts
      const mmfReceiptDebitAmount = assetDividendLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const mmfReceiptCreditDifference = new Decimal(mmfReceiptDbTotalAmount)
        .minus(mmfReceiptCreditAmount)
        .toNumber();
      const mmfReceiptDebitDifference = new Decimal(mmfReceiptDbTotalAmount)
        .minus(mmfReceiptDebitAmount)
        .toNumber();
      const mmfReceiptIsValid = mmfReceiptCreditDifference === 0 && mmfReceiptDebitDifference === 0;

      const mmfReceiptResult: ValidationResult = {
        transactionType: "dividends_mmf_receipt",
        isValid: mmfReceiptIsValid,
        dbTotalAmount: mmfReceiptDbTotalAmount,
        ledgerTotalAmount: mmfReceiptCreditAmount,
        difference: mmfReceiptCreditDifference,
        transactionCount: mmfDividends.length,
        ledgerEntryCount: assetDividendLedgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return mmfDividendIds.has(transactionId);
        }).length
      };

      if (!mmfReceiptIsValid) {
        mmfReceiptResult.discrepancies = await AccountingValidationService._findMMFDividendReceiptDiscrepancies(
          mmfDividends,
          assetDividendLedgerEntries
        );
      }

      // MMF Dividend Commission Validation (SavingsDividendTransaction - Commission flow)
      const mmfCommissionDbTotalAmount = mmfDividends
        .reduce((sum, dividend) => {
          const commissionAmount = new Decimal(dividend.fees.commission.amount || 0);
          return sum.plus(commissionAmount);
        }, new Decimal(0))
        .toNumber();

      // Check debits from client accounts for MMF dividend commissions
      const mmfCommissionDebitAmount = mmfCommissionLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits to MMF dividend fee income account for commissions
      const mmfCommissionCreditAmount = mmfCommissionLedgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            mmfDividendIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.MMF_DIVIDEND_FEES_WH
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const mmfCommissionDebitDifference = new Decimal(mmfCommissionDbTotalAmount)
        .minus(mmfCommissionDebitAmount)
        .toNumber();
      const mmfCommissionCreditDifference = new Decimal(mmfCommissionDbTotalAmount)
        .minus(mmfCommissionCreditAmount)
        .toNumber();
      const mmfCommissionIsValid = mmfCommissionDebitDifference === 0 && mmfCommissionCreditDifference === 0;

      const mmfCommissionResult: ValidationResult = {
        transactionType: "dividends_mmf_commission",
        isValid: mmfCommissionIsValid,
        dbTotalAmount: mmfCommissionDbTotalAmount,
        ledgerTotalAmount: mmfCommissionDebitAmount,
        difference: mmfCommissionDebitDifference,
        transactionCount: mmfDividends.filter((d) => (d.fees.commission.amount || 0) > 0).length,
        ledgerEntryCount: mmfCommissionLedgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return mmfDividendIds.has(transactionId);
        }).length
      };

      if (!mmfCommissionIsValid) {
        mmfCommissionResult.discrepancies =
          await AccountingValidationService._findMMFDividendCommissionDiscrepancies(
            mmfDividends,
            mmfCommissionLedgerEntries
          );
      }

      logger.info("Dividends validation completed", {
        module: "AccountingValidationService",
        method: "validateDividendsDbWithLedger",
        data: { assetDividendsResult, mmfReceiptResult, mmfCommissionResult }
      });

      return [assetDividendsResult, mmfReceiptResult, mmfCommissionResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during dividends validation", {
        module: "AccountingValidationService",
        method: "validateDividendsDbWithLedger",
        data: { error }
      });
      // Return empty results on error to maintain consistent interface
      return [];
    }
  }

  /**
   * Validates that order transaction amounts and commission fees match their accounting ledger entries
   * Validates four types of trading flows according to PRD_ACCOUNTING.md specifications
   *
   * Checks for Asset Buy Orders (linked to AssetTransaction)
   * Checks for Asset Sell Orders (linked to AssetTransaction)
   * Checks for MMF Buy Orders (linked to SavingsTopupTransaction)
   * Checks for MMF Sell Orders (linked to SavingsWithdrawalTransaction)
   *
   * Excludes: Orders linked to custody charge transactions (sell orders from custody fees)
   * Includes: Orders from rebalance transactions, regular asset transactions
   */
  public static async validateOrdersDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting orders validation", {
      module: "AccountingValidationService",
      method: "validateOrdersDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for asset trades with date filter
      const assetBuyLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_BUY,
        fromDate
      );
      const assetSellLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_SELL,
        fromDate
      );

      const ledgerEntries = [...assetBuyLedgerEntries, ...assetSellLedgerEntries];

      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Query all matched orders from the specified date, excluding custody charges
      const orders = await Order.find({
        "consideration.currency": "EUR",
        $or: [{ status: "Matched" }, { status: "Settled" }],
        filledAt: { $gte: new Date(fromDate) }
      }).populate({
        path: "transaction",
        select: "category chargeType"
      });

      // Filter out orders linked to custody charge transactions
      const validOrders = orders.filter((order) => {
        const transaction = order.transaction as any;
        return transaction.category !== "ChargeTransaction";
      });

      // Separate orders by transaction type and side
      const assetBuyOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return (
          order.side === "Buy" &&
          (transaction.category === "AssetTransaction" || transaction.category === "RebalanceTransaction")
        );
      });

      const assetSellOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return (
          order.side === "Sell" &&
          (transaction.category === "AssetTransaction" || transaction.category === "RebalanceTransaction")
        );
      });

      const mmfBuyOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return order.side === "Buy" && transaction.category === "SavingsTopupTransaction";
      });

      const mmfSellOrders = validOrders.filter((order) => {
        const transaction = order.transaction as any;
        return order.side === "Sell" && transaction.category === "SavingsWithdrawalTransaction";
      });

      // Validate each order type
      const assetBuyResult = await AccountingValidationService._validateOrderType(
        assetBuyOrders,
        ledgerEntries,
        clientAccountCodes,
        "asset_buy",
        "Buy"
      );

      const assetSellResult = await AccountingValidationService._validateOrderType(
        assetSellOrders,
        ledgerEntries,
        clientAccountCodes,
        "asset_sell",
        "Sell"
      );

      const mmfBuyResult = await AccountingValidationService._validateOrderType(
        mmfBuyOrders,
        ledgerEntries,
        clientAccountCodes,
        "mmf_buy",
        "Buy"
      );

      const mmfSellResult = await AccountingValidationService._validateOrderType(
        mmfSellOrders,
        ledgerEntries,
        clientAccountCodes,
        "mmf_sell",
        "Sell"
      );

      // Validate internally filled MMF orders
      const internallyFilledResult = await AccountingValidationService.validateInternallyFilledMMFOrders(fromDate);

      logger.info("Orders validation completed", {
        module: "AccountingValidationService",
        method: "validateOrdersDbWithLedger",
        data: { assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult }
      });

      return [assetBuyResult, assetSellResult, mmfBuyResult, mmfSellResult, internallyFilledResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during orders validation", {
        module: "AccountingValidationService",
        method: "validateOrdersDbWithLedger",
        data: { error }
      });
      return [];
    }
  }

  /**
   * Validates that internally filled MMF orders generate no ledger entries
   * Internally filled orders are created when MMF topups and withdrawals are matched internally
   * without going through external broker execution. These orders should generate no ledger entries
   * since there's no actual asset movement - they are just internal bookkeeping.
   *
   * @param fromDate - ISO date string to filter orders from this date onwards
   * @returns Promise<ValidationResult> - Validation result for internally filled orders
   */
  public static async validateInternallyFilledMMFOrders(fromDate: string): Promise<ValidationResult> {
    logger.info("Starting internally filled MMF orders validation", {
      module: "AccountingValidationService",
      method: "validateInternallyFilledMMFOrders",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for asset trades with date filter
      const assetBuyLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_BUY,
        fromDate
      );
      const assetSellLedgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.ASSET_SELL,
        fromDate
      );

      const ledgerEntries = [...assetBuyLedgerEntries, ...assetSellLedgerEntries];

      // Query all internally filled MMF orders from the specified date
      const internallyFilledOrders = await Order.find({
        "consideration.currency": "EUR",
        status: "InternallyFilled",
        filledAt: { $gte: new Date(fromDate) }
      }).populate({
        path: "transaction",
        select: "category"
      });

      // Filter for MMF orders only (linked to SavingsTopupTransaction or SavingsWithdrawalTransaction)
      const mmfInternallyFilledOrders = internallyFilledOrders.filter((order) => {
        const transaction = order.transaction as any;
        return (
          transaction.category === "SavingsTopupTransaction" ||
          transaction.category === "SavingsWithdrawalTransaction"
        );
      });

      // Calculate total amount of internally filled orders (should be 0 in ledger)
      const dbTotalAmount = mmfInternallyFilledOrders
        .reduce((sum, order) => {
          const amount = Decimal.div(order.consideration.amount || 0, 100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      // Get order IDs for ledger entry filtering
      const orderIds = new Set(mmfInternallyFilledOrders.map((order) => order._id.toString()));

      // Check if any ledger entries exist for these internally filled orders
      const relatedLedgerEntries = ledgerEntries.filter((entry) => {
        return orderIds.has(entry.document_id);
      });

      // Calculate total ledger amount (should be 0 for internally filled orders)
      const ledgerTotalAmount = relatedLedgerEntries
        .reduce((sum, entry) => Decimal.add(sum, entry.amount), new Decimal(0))
        .toNumber();

      // Validation passes if no ledger entries exist for internally filled orders
      const isValid = ledgerTotalAmount === 0;
      const difference = ledgerTotalAmount; // Should be 0

      const result: ValidationResult = {
        transactionType: "mmf_internally_filled",
        isValid,
        dbTotalAmount,
        ledgerTotalAmount,
        difference,
        transactionCount: mmfInternallyFilledOrders.length,
        ledgerEntryCount: relatedLedgerEntries.length
      };

      // If validation fails, create discrepancies for each order that has ledger entries
      if (!isValid) {
        result.discrepancies = await AccountingValidationService._findInternallyFilledMMFOrderDiscrepancies(
          mmfInternallyFilledOrders,
          relatedLedgerEntries
        );
      }

      logger.info("Internally filled MMF orders validation completed", {
        module: "AccountingValidationService",
        method: "validateInternallyFilledMMFOrders",
        data: { result }
      });

      return result;
    } catch (error) {
      captureException(error);
      logger.error("Error during internally filled MMF orders validation", {
        module: "AccountingValidationService",
        method: "validateInternallyFilledMMFOrders",
        data: { error }
      });
      // Return empty result on error
      return {
        transactionType: "mmf_internally_filled",
        isValid: false,
        dbTotalAmount: 0,
        ledgerTotalAmount: 0,
        difference: 0,
        transactionCount: 0,
        ledgerEntryCount: 0
      };
    }
  }

  /**
   * Validates that reward transaction amounts match their accounting ledger entries
   * Validates two types of reward flows according to accounting system specifications
   *
   * Checks for Reward Deposit Settlement
   * => Validates bonus expense entries when reward deposits become settled
   * => Debits: CLIENTS_ACCOUNTS_OMNIBUS (money flows into omnibus for the user)
   * => Credits: BONUS_EXPENSE (company expense for giving bonus)
   * => Uses BONUS event type for ledger entry matching
   *
   * Checks for Reward Order Settlement
   * => Validates asset order entries and fee charges when reward orders become matched
   * => Similar to regular asset buy orders but with different fee structure
   * => Uses BONUS event type for ledger entry matching
   *
   * @param fromDate - ISO date string to filter rewards from this date onwards
   * @returns Promise<ValidationResult[]> - Array with 2 validation results (deposit, order)
   */
  public static async validateRewardsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting rewards validation", {
      module: "AccountingValidationService",
      method: "validateRewardsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bonus transactions with date filter
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BONUS,
        fromDate
      );

      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Reward Deposit Settlement Validation
      // Query rewards that had their deposits settled on or after fromDate
      const depositSettledRewards = await Reward.find({
        "consideration.currency": "EUR",
        "deposit.providers.wealthkernel.status": "Settled",
        "deposit.providers.wealthkernel.submittedAt": { $gte: new Date(fromDate) }
      }).populate("targetUser");

      const depositDbTotalAmount = depositSettledRewards
        .reduce((sum, reward) => {
          const amount = new Decimal(reward.consideration.bonusAmount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const depositRewardIds = new Set(depositSettledRewards.map((r) => r._id.toString()));

      // Check credits to BONUS_EXPENSE account (company expense)
      const depositCreditAmount = ledgerEntries
        .filter((entry) => {
          const rewardId = entry.document_id;
          return (
            depositRewardIds.has(rewardId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.BONUS_EXPENSE
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits to CLIENTS_ACCOUNTS_OMNIBUS (money flows into omnibus)
      const depositDebitAmount = ledgerEntries
        .filter((entry) => {
          const rewardId = entry.document_id;
          return (
            depositRewardIds.has(rewardId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const depositCreditDifference = new Decimal(depositDbTotalAmount).minus(depositCreditAmount).toNumber();
      const depositDebitDifference = new Decimal(depositDbTotalAmount).minus(depositDebitAmount).toNumber();
      const depositIsValid = depositCreditDifference === 0 && depositDebitDifference === 0;

      const depositResult: ValidationResult = {
        transactionType: "rewards_deposit",
        isValid: depositIsValid,
        dbTotalAmount: depositDbTotalAmount,
        ledgerTotalAmount: depositCreditAmount,
        difference: depositCreditDifference,
        transactionCount: depositSettledRewards.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const rewardId = entry.document_id;
          return (
            depositRewardIds.has(rewardId) &&
            ((entry.account_code === LedgerAccounts.BONUS_EXPENSE && entry.side === "credit") ||
              (entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit"))
          );
        }).length
      };

      if (!depositIsValid) {
        depositResult.discrepancies = await AccountingValidationService._findRewardDepositDiscrepancies(
          depositSettledRewards,
          ledgerEntries
        );
      }

      // Reward Order Settlement Validation
      // Query rewards that became settled (order matched) on or after fromDate
      const orderSettledRewards = await Reward.find({
        "consideration.currency": "EUR",
        "order.providers.wealthkernel.status": "Matched",
        "order.providers.wealthkernel.submittedAt": { $gte: new Date(fromDate) }
      }).populate("targetUser");

      // Calculate total client-side debits (net order amount + total commission)
      const orderDbTotalAmount = orderSettledRewards
        .reduce((sum, reward) => {
          const orderAmount = new Decimal(reward.consideration.orderAmount);
          const fxFeeAmount = new Decimal(reward.fees?.fx?.amount || 0).mul(100); // Convert to cents
          const brokerFeeAmount = new Decimal(
            reward.order?.providers?.wealthkernel?.accountingBrokerFxFee || 0
          ).mul(100); // Convert to cents

          // Net order amount = order amount - broker fee (matches accounting service logic)
          const netOrderAmount = orderAmount.minus(brokerFeeAmount);

          // Total commission = FX fee + broker fee
          const totalCommissionAmount = fxFeeAmount.plus(brokerFeeAmount);

          // Total client impact = net order amount + total commission
          return sum.plus(netOrderAmount).plus(totalCommissionAmount);
        }, new Decimal(0))
        .div(100)
        .toNumber();

      const orderRewardIds = new Set(orderSettledRewards.map((r) => r._id.toString()));

      // Calculate total client-side ledger amounts (debits from client accounts)
      const orderDebitAmount = ledgerEntries
        .filter((entry) => {
          const rewardId = entry.document_id;
          return (
            orderRewardIds.has(rewardId) &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Calculate total omnibus credit amount in ledger for these rewards
      const orderCreditAmount = ledgerEntries
        .filter((entry) => {
          const rewardId = entry.document_id;
          return (
            orderRewardIds.has(rewardId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Expected omnibus credits:
      //   Movement entry credits omnibus with (orderAmount - brokerFee)
      //   Expense entry credits omnibus with brokerFee
      // => Total credit equals full orderAmount

      const expectedOmnibusAmount = orderSettledRewards
        .reduce((sum, reward) => {
          const orderAmountCents = new Decimal(reward.consideration.orderAmount); // cents
          return sum.plus(orderAmountCents);
        }, new Decimal(0))
        .div(100)
        .toNumber();

      // Validation - client debits should match expected total, omnibus credits should match net amounts
      const orderDebitDifference = new Decimal(orderDbTotalAmount).minus(orderDebitAmount).toNumber();
      const orderCreditDifference = new Decimal(expectedOmnibusAmount).minus(orderCreditAmount).toNumber();

      let orderIsValid = orderDebitDifference === 0 && orderCreditDifference === 0;

      // Additional validation: Check that ALL reward orders have revenue entries with invoice references
      const rewardOrdersWithoutRevenueEntries = orderSettledRewards.filter((reward) => {
        const rewardId = reward._id.toString();

        // Check if this reward has revenue entries (entries with reference_number)
        const hasRevenueEntries = ledgerEntries.some((entry) => {
          const entryRewardId = entry.document_id;
          return (
            entryRewardId === rewardId && entry.reference_number !== null && entry.reference_number !== undefined
          );
        });

        return !hasRevenueEntries;
      });

      // If any reward orders are missing revenue entries, validation fails
      if (rewardOrdersWithoutRevenueEntries.length > 0) {
        orderIsValid = false;
      }

      const orderResult: ValidationResult = {
        transactionType: "rewards_order",
        isValid: orderIsValid,
        dbTotalAmount: orderDbTotalAmount,
        ledgerTotalAmount: orderDebitAmount,
        difference: orderDebitDifference,
        transactionCount: orderSettledRewards.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const rewardId = entry.document_id;
          return (
            orderRewardIds.has(rewardId) &&
            ((clientAccountCodes.has(entry.account_code as LedgerAccounts) && entry.side === "debit") ||
              (entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "credit") ||
              (entry.account_code === LedgerAccounts.COMMISSION_FEES_WH && entry.side === "credit"))
          );
        }).length
      };

      if (!orderIsValid) {
        orderResult.discrepancies = await AccountingValidationService._findRewardOrderDiscrepancies(
          orderSettledRewards,
          ledgerEntries,
          rewardOrdersWithoutRevenueEntries
        );
      }

      logger.info("Rewards validation completed", {
        module: "AccountingValidationService",
        method: "validateRewardsDbWithLedger",
        data: { depositResult, orderResult }
      });

      return [depositResult, orderResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during rewards validation", {
        module: "AccountingValidationService",
        method: "validateRewardsDbWithLedger",
        data: { error }
      });
      return [];
    }
  }

  /**
   * Validates that gift transaction amounts match their accounting ledger entries
   * Similar to reward deposits but for gift settlements only
   *
   * Checks for Gift Deposit Settlement
   * => Validates bonus expense entries when gift deposits become settled
   * => Debits: CLIENTS_ACCOUNTS_OMNIBUS (money flows into omnibus for the user)
   * => Credits: BONUS_EXPENSE (company expense for giving gift)
   * => Uses BONUS event type for ledger entry matching
   *
   * @param fromDate - ISO date string to filter gifts from this date onwards
   * @returns Promise<ValidationResult[]> - Array with 1 validation result (deposit)
   */
  public static async validateGiftsDbWithLedger(fromDate: string): Promise<ValidationResult[]> {
    logger.info("Starting gifts validation", {
      module: "AccountingValidationService",
      method: "validateGiftsDbWithLedger",
      data: { fromDate }
    });

    try {
      // Get all ledger entries for bonus transactions with date filter
      const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntriesByEventType(
        AccountingEventType.BONUS,
        fromDate
      );

      // Gift Deposit Settlement Validation
      // Query gifts that had their deposits settled on or after fromDate
      const depositSettledGifts = await Gift.find({
        "consideration.currency": "EUR",
        "deposit.providers.wealthkernel.status": "Settled",
        "deposit.providers.wealthkernel.submittedAt": { $gte: new Date(fromDate) }
      });

      const depositDbTotalAmount = depositSettledGifts
        .reduce((sum, gift) => {
          const amount = new Decimal(gift.consideration.amount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const depositGiftIds = new Set(depositSettledGifts.map((g) => g._id.toString()));

      // Check credits to BONUS_EXPENSE account (company expense)
      const depositCreditAmount = ledgerEntries
        .filter((entry) => {
          const giftId = entry.document_id;
          return (
            depositGiftIds.has(giftId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.BONUS_EXPENSE
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits to CLIENTS_ACCOUNTS_OMNIBUS (money flows into omnibus)
      const depositDebitAmount = ledgerEntries
        .filter((entry) => {
          const giftId = entry.document_id;
          return (
            depositGiftIds.has(giftId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Both debit and credit sides should match db total
      const depositCreditDifference = new Decimal(depositDbTotalAmount).minus(depositCreditAmount).toNumber();
      const depositDebitDifference = new Decimal(depositDbTotalAmount).minus(depositDebitAmount).toNumber();
      const depositIsValid = depositCreditDifference === 0 && depositDebitDifference === 0;

      const depositResult: ValidationResult = {
        transactionType: "gifts_deposit",
        isValid: depositIsValid,
        dbTotalAmount: depositDbTotalAmount,
        ledgerTotalAmount: depositCreditAmount,
        difference: depositCreditDifference,
        transactionCount: depositSettledGifts.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const giftId = entry.document_id;
          return (
            depositGiftIds.has(giftId) &&
            ((entry.account_code === LedgerAccounts.BONUS_EXPENSE && entry.side === "credit") ||
              (entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit"))
          );
        }).length
      };

      if (!depositIsValid) {
        depositResult.discrepancies = await AccountingValidationService._findGiftDepositDiscrepancies(
          depositSettledGifts,
          ledgerEntries
        );
      }

      logger.info("Gifts validation completed", {
        module: "AccountingValidationService",
        method: "validateGiftsDbWithLedger",
        data: { depositResult }
      });

      return [depositResult];
    } catch (error) {
      captureException(error);
      logger.error("Error during gifts validation", {
        module: "AccountingValidationService",
        method: "validateGiftsDbWithLedger",
        data: { error }
      });
      return [];
    }
  }

  /**
   * Ledger ↔ WealthKernel cash reconciliation validation method
   * Compares WealthKernel cash balance snapshots with ledger entries for a given period
   * This method operates independently of the existing DB ↔ Ledger validation suite
   *
   * @param fromDate ISO date string (YYYY-MM-DD) for the start of the reconciliation period
   * @returns CashReconciliationResult with per-account and aggregate deltas
   */
  public static async validateCashLedgerReconciliation(fromDate: string): Promise<CashReconciliationResult> {
    logger.info("Starting cash ledger reconciliation", {
      module: "AccountingValidationService",
      method: "validateCashLedgerReconciliation",
      data: { fromDate }
    });

    try {
      const perAccount: Record<LedgerAccounts, CashReconDelta> = {} as Record<LedgerAccounts, CashReconDelta>;
      let aggregateStartBalance = 0;
      let aggregateEndBalance = 0;
      let aggregateLedgerNetChange = 0;

      // Get the list of accounts to validate (only those with WK mappings)
      const accountsToValidate = Object.keys(CASH_ACCOUNTS_WK_MAPPING).filter(
        (accountCode) =>
          CASH_ACCOUNTS_WK_MAPPING[accountCode as LedgerAccounts] !== undefined ||
          accountCode === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
      ) as LedgerAccounts[];

      logger.info(`Validating cash reconciliation for ${accountsToValidate.length} accounts`, {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: { accounts: accountsToValidate }
      });

      // Loop through each account
      for (const accountCode of accountsToValidate) {
        // 1. Fetch the starting balance from cash balances table
        const startingSnapshot = await AccountingLedgerStorageService.getCashBalanceOnOrAfterDate(
          accountCode,
          fromDate
        );
        const startBalance = startingSnapshot?.balance || 0;

        // 2. Fetch the ending balance (latest snapshot)
        const endingSnapshot = await AccountingLedgerStorageService.getLatestCashBalance(accountCode);
        const endBalance = endingSnapshot?.balance || 0;

        // 3. Calculate WK delta
        const deltaWK = new Decimal(endBalance).minus(startBalance).toNumber();

        // 4. Query ledger entries for this account between fromDate and now
        const ledgerEntries = await AccountingLedgerStorageService.queryLedgerEntries({
          account_code: accountCode,
          article_date_from: fromDate
        });

        // 5. Calculate ledger net change (debits - credits)
        const deltaLedger = ledgerEntries
          .reduce((net, entry) => {
            if (entry.side === "debit") {
              return net.plus(entry.amount);
            } else {
              return net.minus(entry.amount);
            }
          }, new Decimal(0))
          .toNumber();

        // 6. Calculate difference (0 tolerance)
        const difference = new Decimal(deltaWK).minus(deltaLedger).toNumber();

        // Store per-account result
        perAccount[accountCode] = {
          deltaWK,
          deltaLedger,
          difference
        };

        // Add to aggregates
        aggregateStartBalance = new Decimal(aggregateStartBalance).plus(startBalance).toNumber();
        aggregateEndBalance = new Decimal(aggregateEndBalance).plus(endBalance).toNumber();
        aggregateLedgerNetChange = new Decimal(aggregateLedgerNetChange).plus(deltaLedger).toNumber();

        logger.info("Account reconciliation completed", {
          module: "AccountingValidationService",
          method: "validateCashLedgerReconciliation",
          data: {
            accountCode,
            startBalance,
            endBalance,
            deltaWK,
            deltaLedger,
            difference,
            ledgerEntryCount: ledgerEntries.length
          }
        });
      }

      // Calculate aggregate results
      const aggregateDeltaWK = new Decimal(aggregateEndBalance).minus(aggregateStartBalance).toNumber();
      const aggregateDifference = new Decimal(aggregateDeltaWK).minus(aggregateLedgerNetChange).toNumber();

      const aggregate: CashReconDelta = {
        deltaWK: aggregateDeltaWK,
        deltaLedger: aggregateLedgerNetChange,
        difference: aggregateDifference
      };

      const result: CashReconciliationResult = {
        fromDate,
        perAccount,
        aggregate
      };

      // Log structured result
      logger.info("Cash ledger reconciliation completed", {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: result
      });

      return result;
    } catch (error) {
      captureException(error);
      logger.error("Error during cash ledger reconciliation", {
        module: "AccountingValidationService",
        method: "validateCashLedgerReconciliation",
        data: { error }
      });
    }
  }

  /**
   * Helper method to validate a specific order type (buy/sell) - NO TOLERANCE
   * Also validates that revenue entries with invoice references are created for ALL orders,
   * even when commission amounts are 0
   */
  private static async _validateOrderType(
    orders: any[],
    ledgerEntries: LedgerQueryResult[],
    clientAccountCodes: Set<LedgerAccounts>,
    transactionType: "asset_buy" | "asset_sell" | "mmf_buy" | "mmf_sell",
    side: "Buy" | "Sell"
  ): Promise<ValidationResult> {
    // Calculate total amounts from database (net client impact including fees and remainders)
    const dbTotalAmount = orders
      .reduce((sum, order) => {
        // Settlement amount includes broker fee, so we need to calculate net amount first
        const settlementAmount = new Decimal(order.consideration.amount || 0).div(100);
        const brokerFee = new Decimal(order.providers?.wealthkernel?.accountingBrokerFxFee || 0);
        const netAmount = settlementAmount.minus(brokerFee);

        const fxFee = new Decimal(order.fees?.fx?.amount || 0);
        const realtimeExecutionFee = new Decimal(order.fees?.realtimeExecution?.amount || 0);
        const whCommission = fxFee.plus(realtimeExecutionFee);

        // Total commission = WH commission + broker commission
        const totalCommission = whCommission.plus(brokerFee);

        // For asset buy orders, include remainder amount
        let remainderAmount = new Decimal(0);
        if (
          side === "Buy" &&
          (transactionType === "asset_buy" || transactionType === "asset_sell") &&
          order.remainder > 0
        ) {
          remainderAmount = new Decimal(order.remainder).div(100);
        }

        // Total client impact = net amount + total commission fees + remainder
        const totalClientImpact = netAmount.plus(totalCommission).plus(remainderAmount);
        return sum.plus(totalClientImpact);
      }, new Decimal(0))
      .toNumber();

    // Get unique order IDs from the orders (not transaction IDs!)
    const orderIds = new Set(
      orders.map((order) => {
        return order._id.toString();
      })
    );

    // Filter ledger entries for these orders
    const orderLedgerEntries = ledgerEntries.filter((entry) => {
      const orderId = entry.document_id;
      return orderIds.has(orderId);
    });

    // Calculate total client-side ledger amounts
    let ledgerClientAmount = 0;

    if (side === "Buy") {
      // For buy orders: sum all client debits (net settlement + commission fees + remainder)
      ledgerClientAmount = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "debit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
    } else {
      // For sell orders: calculate absolute total client-side activity (proceeds + commission fees)
      const clientCredits = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "credit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      const clientDebits = orderLedgerEntries
        .filter((entry) => {
          const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
          return entry.side === "debit" && isClientAccount;
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // For sell orders, total client activity = proceeds + commission fees
      ledgerClientAmount = new Decimal(clientCredits).plus(clientDebits).toNumber();
    }

    const difference = new Decimal(dbTotalAmount).minus(ledgerClientAmount).toNumber();

    // For asset buy orders with remainders, the difference should equal the total remainder amount
    let expectedDifference = 0;
    if (side === "Buy" && transactionType === "asset_buy") {
      expectedDifference = orders
        .reduce((sum, order) => {
          if (order.remainder > 0) {
            return sum.plus(new Decimal(order.remainder).div(100));
          }
          return sum;
        }, new Decimal(0))
        .toNumber();
    }

    let isValid = difference === expectedDifference;

    // Additional validation: Check that ALL orders have revenue entries with invoice references
    // This ensures that even orders with 0 commission generate the required revenue entries
    const ordersWithoutRevenueEntries = orders.filter((order) => {
      const orderId = order._id.toString();

      // Check if this order has revenue entries (entries with reference_number)
      const hasRevenueEntries = orderLedgerEntries.some((entry) => {
        const entryOrderId = entry.document_id;
        return entryOrderId === orderId && entry.reference_number !== null && entry.reference_number !== undefined;
      });

      return !hasRevenueEntries;
    });

    // If any orders are missing revenue entries, validation fails
    if (ordersWithoutRevenueEntries.length > 0) {
      isValid = false;
    }

    const result: ValidationResult = {
      transactionType,
      isValid,
      dbTotalAmount,
      ledgerTotalAmount: ledgerClientAmount,
      difference,
      transactionCount: orders.length,
      ledgerEntryCount: orderLedgerEntries.length
    };

    if (!isValid) {
      result.discrepancies = await AccountingValidationService._findOrderDiscrepancies(
        orders,
        orderLedgerEntries,
        side,
        ordersWithoutRevenueEntries
      );
    }

    return result;
  }

  /**
   * Finds individual discrepancies between orders and their ledger entries
   * Also reports orders that are missing revenue entries (invoice references)
   */
  private static async _findOrderDiscrepancies(
    orders: any[],
    ledgerEntries: LedgerQueryResult[],
    side: "Buy" | "Sell",
    ordersWithoutRevenueEntries: any[] = []
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const order of orders) {
      const orderId = order._id.toString();

      // Calculate total client impact including commission fees and remainders
      const settlementAmount = new Decimal(order.consideration.amount || 0).div(100);
      const brokerFee = new Decimal(order.providers?.wealthkernel?.accountingBrokerFxFee || 0);
      const netAmount = settlementAmount.minus(brokerFee);

      const fxFee = new Decimal(order.fees?.fx?.amount || 0);
      const realtimeExecutionFee = new Decimal(order.fees?.realtimeExecution?.amount || 0);
      const whCommission = fxFee.plus(realtimeExecutionFee);

      // Total commission = WH commission + broker commission
      const totalCommission = whCommission.plus(brokerFee);

      // For asset buy orders, include remainder amount
      let remainderAmount = new Decimal(0);
      if (side === "Buy" && order.remainder > 0) {
        // Check if this is an asset transaction order by looking at the transaction category
        if (
          order.transaction &&
          (order.transaction.category === "AssetTransaction" ||
            order.transaction.category === "RebalanceTransaction")
        ) {
          remainderAmount = new Decimal(order.remainder).div(100);
        }
      }

      // Total client impact = net amount + total commission fees + remainder
      const dbTotalAmount = netAmount.plus(totalCommission).plus(remainderAmount).toNumber();

      // Find all ledger entries for this order (order ID in description)
      const orderEntries = ledgerEntries.filter((entry) => {
        const entryOrderId = entry.document_id;
        return entryOrderId === orderId;
      });

      // Calculate total client-side ledger amount
      let ledgerClientAmount = 0;

      if (side === "Buy") {
        ledgerClientAmount = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "debit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();
      } else {
        // For sell orders: sum absolute client activity (proceeds + commission fees)
        const clientCredits = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "credit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();

        const clientDebits = orderEntries
          .filter((entry) => {
            const isClientAccount = clientAccountCodes.has(entry.account_code as LedgerAccounts);
            return entry.side === "debit" && isClientAccount;
          })
          .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
          .toNumber();

        ledgerClientAmount = new Decimal(clientCredits).plus(clientDebits).toNumber();
      }

      const difference = new Decimal(dbTotalAmount).minus(ledgerClientAmount).toNumber();

      // Check if this order is missing revenue entries
      const isMissingRevenueEntries = ordersWithoutRevenueEntries.some(
        (missingOrder) => missingOrder._id.toString() === orderId
      );

      // Report discrepancy if amounts don't match OR if revenue entries are missing
      if (Math.abs(difference) > 0 || isMissingRevenueEntries) {
        let description = "";
        if (isMissingRevenueEntries && Math.abs(difference) > 0) {
          description = `Order ${side}: Missing revenue entries AND amount mismatch (DB=€${dbTotalAmount} vs Ledger=€${ledgerClientAmount})`;
        } else if (isMissingRevenueEntries) {
          description = `Order ${side}: Missing revenue entries`;
        } else {
          description = `Order ${side}: DB amount (€${dbTotalAmount}) doesn't match client account ledger entries (€${ledgerClientAmount})`;
        }

        discrepancies.push({
          transactionId: orderId,
          dbAmount: dbTotalAmount,
          ledgerAmount: ledgerClientAmount,
          difference: isMissingRevenueEntries ? -999999 : difference, // Use special marker for missing revenue entries
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between deposits and their ledger entries
   * Used for Stage 1 and Stage 3 deposits (client account credits)
   */
  private static async _findDepositDiscrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = Decimal.div(deposit.consideration.amount || 0, 100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `Stage 1: DB amount (€${dbAmount}) doesn't match client account credits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between Stage 2 deposits and their ledger entries
   * Stage 2: Intermediary #1 → Intermediary #2 (instant flow only)
   * Checks credits to INTERMEDIARY_DEPOSITS_1 and debits to INTERMEDIARY_DEPOSITS_2
   */
  private static async _findDepositStage2Discrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = Decimal.div(deposit.consideration.amount || 0, 100).toNumber();

      // Find credit entries to INTERMEDIARY_DEPOSITS_1 for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
        );
      });

      // Find debit entries to INTERMEDIARY_DEPOSITS_2 for this transaction
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "debit" &&
          entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_2
        );
      });

      // Sum credit amounts (should match db amount)
      const creditLedgerAmount = creditEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
      const debitLedgerAmount = debitEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
      const difference = new Decimal(dbAmount).minus(creditLedgerAmount).toNumber();

      if (Math.abs(difference) > 0 || creditLedgerAmount !== debitLedgerAmount) {
        const issues = [];
        if (Math.abs(difference) > 0) {
          issues.push(`Amount mismatch: DB=${dbAmount}€ vs INTERMEDIARY_DEPOSITS_1 Credit=${creditLedgerAmount}€`);
        }
        if (creditLedgerAmount !== debitLedgerAmount) {
          issues.push(
            `Credit/Debit imbalance: INTERMEDIARY_DEPOSITS_1 Credit=${creditLedgerAmount}€ vs INTERMEDIARY_DEPOSITS_2 Debit=${debitLedgerAmount}€`
          );
        }
        const description = `Stage 2 (instant flow): ${issues.join("; ")}`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount: creditLedgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between Stage 3 deposits and their ledger entries
   * Stage 3: Intermediary → Omnibus (WealthKernel settlement)
   * Checks debits to CLIENTS_ACCOUNTS_OMNIBUS and credits from either INTERMEDIARY_DEPOSITS_1 or INTERMEDIARY_DEPOSITS_2
   */
  private static async _findDepositStage3Discrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = Decimal.div(deposit.consideration.amount || 0, 100).toNumber();

      // Determine if this is an instant flow transaction
      let isInstantFlow = false;
      if (deposit.linkedCreditTicket) {
        try {
          const creditTicket = await CreditTicket.findById(deposit.linkedCreditTicket);
          // Check if credit ticket was credited (either currently "Credited" or "Settled")
          isInstantFlow = creditTicket?.status === "Credited" || creditTicket?.status === "Settled";
        } catch (error) {
          logger.warn(`Failed to fetch credit ticket for deposit validation: ${deposit.linkedCreditTicket}`, {
            module: "AccountingValidationService",
            method: "_findDepositStage3Discrepancies",
            data: { transactionId, error }
          });
        }
      }

      // Find debit entries to CLIENTS_ACCOUNTS_OMNIBUS for this transaction
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "debit" &&
          entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
        );
      });

      // Find credit entries from the CORRECT intermediary account based on flow type
      const expectedCreditAccount = isInstantFlow
        ? LedgerAccounts.INTERMEDIARY_DEPOSITS_2
        : LedgerAccounts.INTERMEDIARY_DEPOSITS_1;

      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          entry.account_code === expectedCreditAccount
        );
      });

      // Also check for incorrect credit entries (wrong intermediary account)
      // Only count entries that are paired with omnibus debits (actual Stage 3 entries)
      const incorrectCreditAccount = isInstantFlow
        ? LedgerAccounts.INTERMEDIARY_DEPOSITS_1
        : LedgerAccounts.INTERMEDIARY_DEPOSITS_2;

      const incorrectCreditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        if (
          entryTransactionId !== transactionId ||
          entry.side !== "credit" ||
          entry.account_code !== incorrectCreditAccount
        ) {
          return false;
        }

        // Only count this as an incorrect Stage 3 entry if it's paired with an omnibus debit (same AA)
        return ledgerEntries.some(
          (debit) =>
            debit.aa === entry.aa &&
            debit.side === "debit" &&
            debit.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS &&
            debit.document_id === transactionId
        );
      });

      // Sum debit amounts (should match db amount)
      const debitLedgerAmount = debitEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
      const creditLedgerAmount = creditEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();
      const incorrectCreditAmount = incorrectCreditEntries
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      const difference = new Decimal(dbAmount).minus(debitLedgerAmount).toNumber();

      // Validation fails if:
      // 1. Amount mismatch between DB and ledger
      // 2. Debit and credit amounts don't match
      // 3. Credit entries are using the wrong intermediary account
      const hasAmountMismatch = Math.abs(difference) > 0;
      const hasDebitCreditMismatch = debitLedgerAmount !== creditLedgerAmount;
      const hasWrongIntermediaryAccount = incorrectCreditAmount > 0;

      if (hasAmountMismatch || hasDebitCreditMismatch || hasWrongIntermediaryAccount) {
        const flowType = isInstantFlow ? "instant" : "standard";
        const expectedAccount = expectedCreditAccount;
        const actualAccount = incorrectCreditAmount > 0 ? incorrectCreditAccount : expectedCreditAccount;

        // Build detailed description of the specific issues
        const issues = [];
        if (hasAmountMismatch) {
          issues.push(`Amount mismatch: DB=${dbAmount}€ vs Omnibus Debit=${debitLedgerAmount}€`);
        }
        if (hasDebitCreditMismatch) {
          issues.push(
            `Debit/Credit imbalance: Omnibus Debit=${debitLedgerAmount}€ vs Intermediary Credit=${creditLedgerAmount}€`
          );
        }
        if (hasWrongIntermediaryAccount) {
          issues.push(
            `Wrong intermediary account: ${flowType} flow expects ${expectedAccount} but found ${incorrectCreditAmount}€ in ${incorrectCreditAccount}`
          );
        }

        const description = `Stage 3 (${flowType} flow): ${issues.join("; ")}`;

        logger.warn("Stage 3 deposit validation failed - flow type mismatch", {
          module: "AccountingValidationService",
          method: "_findDepositStage3Discrepancies",
          data: {
            transactionId,
            flowType,
            expectedCreditAccount: expectedAccount,
            actualCreditAccount: actualAccount,
            incorrectCreditAmount,
            isInstantFlow,
            hasAmountMismatch,
            hasDebitCreditMismatch,
            hasWrongIntermediaryAccount,
            description
          }
        });

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount: debitLedgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between withdrawals and their ledger entries
   */
  private static async _findWithdrawalDiscrepancies(
    withdrawals: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const withdrawal of withdrawals) {
      const transactionId = withdrawal._id.toString();
      const dbAmount = Decimal.div(withdrawal.consideration.amount || 0, 100).toNumber();

      // Check if withdrawal has reached Stage 2 (Devengo outgoing confirmed)
      const hasReachedStage2 =
        withdrawal.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
        "confirmed";

      let debitEntries;
      if (hasReachedStage2) {
        // Stage 2: Find debit entries from client accounts (Intermediary → Client)
        debitEntries = ledgerEntries.filter((entry) => {
          const entryTransactionId = entry.document_id;
          return (
            entryTransactionId === transactionId &&
            entry.side === "debit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        });
      } else {
        // Stage 1: Find debit entries from intermediary withdrawals account (Omnibus → Intermediary)
        debitEntries = ledgerEntries.filter((entry) => {
          const entryTransactionId = entry.document_id;
          return (
            entryTransactionId === transactionId &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_WITHDRAWALS
          );
        });
      }

      // Sum debit amounts
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const stage = hasReachedStage2 ? "Stage 2" : "Stage 1";
        const account = hasReachedStage2 ? "client accounts" : "INTERMEDIARY_WITHDRAWALS";
        const description = `${stage}: DB amount (€${dbAmount}) doesn't match ${account} debits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between asset dividends and their ledger entries
   */
  private static async _findAssetDividendDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.consideration.amount || 0).div(100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `Asset dividend: DB amount (€${dbAmount}) doesn't match client account credits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between MMF dividend receipts and their ledger entries
   */
  private static async _findMMFDividendReceiptDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.originalDividendAmount || 0).div(100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `MMF dividend receipt: DB amount (€${dbAmount}) doesn't match client account credits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between MMF dividend commissions and their ledger entries
   */
  private static async _findMMFDividendCommissionDiscrepancies(
    dividends: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const dividend of dividends) {
      const transactionId = dividend._id.toString();
      const dbAmount = new Decimal(dividend.fees.commission.amount || 0).toNumber();

      // Skip dividends with no commission
      if (dbAmount <= 0) continue;

      // Find debit entries from client accounts for this transaction
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "debit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum debit amounts from client accounts
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `MMF dividend commission: DB amount (€${dbAmount}) doesn't match revenue account debits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between reward deposits and their ledger entries
   */
  private static async _findRewardDepositDiscrepancies(
    rewards: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    for (const reward of rewards) {
      const rewardId = reward._id.toString();
      const dbAmount = new Decimal(reward.consideration.bonusAmount || 0).div(100).toNumber();

      // Find credit entries to BONUS_EXPENSE account for this reward
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryRewardId = entry.document_id;
        return (
          entryRewardId === rewardId &&
          entry.side === "credit" &&
          entry.account_code === LedgerAccounts.BONUS_EXPENSE
        );
      });

      // Sum credit amounts to bonus expense account
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `Reward order: DB amount (€${dbAmount}) doesn't match client account ledger entries (€${ledgerAmount})`;

        discrepancies.push({
          transactionId: rewardId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between reward orders and their ledger entries
   * Also reports reward orders that are missing revenue entries (invoice references)
   */
  private static async _findRewardOrderDiscrepancies(
    rewards: any[],
    ledgerEntries: LedgerQueryResult[],
    rewardsWithoutRevenueEntries: any[] = []
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const reward of rewards) {
      const rewardId = reward._id.toString();

      // Calculate total client-side impact (net order amount + total commission)
      const orderAmount = new Decimal(reward.consideration.orderAmount || 0);
      const fxFeeAmount = new Decimal(reward.fees?.fx?.amount || 0).mul(100); // Convert to cents
      const brokerFeeAmount = new Decimal(reward.order?.providers?.wealthkernel?.accountingBrokerFxFee || 0).mul(
        100
      ); // Convert to cents

      // Net order amount = order amount - broker fee (matches accounting service logic)
      const netOrderAmount = orderAmount.minus(brokerFeeAmount);

      // Total commission = FX fee + broker fee
      const totalCommissionAmount = fxFeeAmount.plus(brokerFeeAmount);

      // Total client impact = net order amount + total commission
      const dbAmount = netOrderAmount.plus(totalCommissionAmount).div(100).toNumber();

      // Find debit entries from client accounts for this reward
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryRewardId = entry.document_id;
        return (
          entryRewardId === rewardId &&
          entry.side === "debit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum debit amounts from client accounts
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      // Check if this reward is missing revenue entries
      const isMissingRevenueEntries = rewardsWithoutRevenueEntries.some(
        (missingReward) => missingReward._id.toString() === rewardId
      );

      // Report discrepancy if amounts don't match OR if revenue entries are missing
      if (Math.abs(difference) > 0 || isMissingRevenueEntries) {
        let description = "";
        if (isMissingRevenueEntries && Math.abs(difference) > 0) {
          description = `Reward order: Missing revenue entries AND amount mismatch (DB=€${dbAmount} vs Ledger=€${ledgerAmount})`;
        } else if (isMissingRevenueEntries) {
          description = "Reward order: Missing revenue entries";
        } else {
          description = `Reward order: DB amount (€${dbAmount}) doesn't match bonus expense account debits (€${ledgerAmount})`;
        }

        discrepancies.push({
          transactionId: rewardId,
          dbAmount,
          ledgerAmount,
          difference: isMissingRevenueEntries ? -999999 : difference, // Use special marker for missing revenue entries
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between gift deposits and their ledger entries
   */
  private static async _findGiftDepositDiscrepancies(
    gifts: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    for (const gift of gifts) {
      const giftId = gift._id.toString();
      const dbAmount = new Decimal(gift.consideration.amount || 0).div(100).toNumber();

      // Find credit entries to BONUS_EXPENSE account for this gift
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryGiftId = entry.document_id;
        return (
          entryGiftId === giftId && entry.side === "credit" && entry.account_code === LedgerAccounts.BONUS_EXPENSE
        );
      });

      // Sum credit amounts to bonus expense account
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `Gift deposit: DB amount (€${dbAmount}) doesn't match bonus expense account credits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId: giftId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between internally filled MMF orders and their ledger entries
   * Internally filled orders should have no ledger entries, so any ledger entries found are discrepancies
   */
  private static async _findInternallyFilledMMFOrderDiscrepancies(
    orders: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    // Filter orders that have ledger entries (these are the problematic ones)
    const ordersWithLedgerEntries = orders.filter((order) => {
      const orderId = order._id.toString();
      return ledgerEntries.some((entry) => entry.document_id === orderId);
    });

    for (const order of ordersWithLedgerEntries) {
      const orderId = order._id.toString();

      // Find all ledger entries for this order
      const orderLedgerEntries = ledgerEntries.filter((entry) => entry.document_id === orderId);

      // Calculate total ledger amount for this order
      const orderLedgerAmount = orderLedgerEntries
        .reduce((sum, entry) => Decimal.add(sum, entry.amount), new Decimal(0))
        .toNumber();

      // For internally filled orders, db amount should be 0 (no ledger impact expected)
      // The ledger amount represents the erroneous entries that shouldn't exist
      const description = `Internally filled MMF order: Should have no ledger entries but found €${orderLedgerAmount} in client accounts`;

      discrepancies.push({
        transactionId: orderId,
        dbAmount: 0, // Internally filled orders should have 0 ledger impact
        ledgerAmount: orderLedgerAmount,
        difference: orderLedgerAmount, // The entire ledger amount is the discrepancy
        description
      });
    }

    return discrepancies;
  }

  /**
   * Validates direct debit deposits with their ledger entries
   * Validates both Stage 1 (Collection) and Stage 2 (Settlement) for direct debits
   */
  private static async _validateDirectDebitDeposits(
    fromDate: string,
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationResult[]> {
    logger.info("Starting direct debit deposits validation", {
      module: "AccountingValidationService",
      method: "_validateDirectDebitDeposits",
      data: { fromDate }
    });

    try {
      const clientAccountCodes = new Set([
        LedgerAccounts.CLIENT_DOMESTIC,
        LedgerAccounts.CLIENT_EU_EEA,
        LedgerAccounts.CLIENT_INTERNATIONAL
      ]);

      // Stage 1 Validation: Devengo direct debit collection completed (only DIRECT_DEBIT_AND_BANK_TRANSFER)
      const stage1DirectDebits = await DepositCashTransaction.find({
        "consideration.currency": "EUR",
        depositMethod: "DIRECT_DEBIT_AND_BANK_TRANSFER",
        "transferWithIntermediary.collection.incomingPayment.providers.devengo.status": "confirmed",
        createdAt: { $gte: new Date(fromDate) },
        _id: { $nin: IGNORED_DEPOSIT_IDS }
      }).populate("owner");

      const stage1DbTotalAmount = stage1DirectDebits
        .reduce((sum, deposit) => {
          const amount = new Decimal(deposit.consideration.amount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage1DepositIds = new Set(stage1DirectDebits.map((d) => d._id.toString()));

      // Check credits on client accounts for direct debit stage 1
      const stage1CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "credit" &&
            clientAccountCodes.has(entry.account_code as LedgerAccounts)
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check debits on intermediary deposits account for stage 1
      const stage1DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      const stage1CreditDifference = new Decimal(stage1DbTotalAmount).minus(stage1CreditAmount).toNumber();
      const stage1DebitDifference = new Decimal(stage1DbTotalAmount).minus(stage1DebitAmount).toNumber();
      const stage1IsValid = stage1CreditDifference === 0 && stage1DebitDifference === 0;

      const stage1Result: ValidationResult = {
        transactionType: "deposits_direct_debit_stage1",
        isValid: stage1IsValid,
        dbTotalAmount: stage1DbTotalAmount,
        ledgerTotalAmount: stage1CreditAmount,
        difference: stage1CreditDifference,
        transactionCount: stage1DirectDebits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage1DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "debit") ||
              (clientAccountCodes.has(entry.account_code as LedgerAccounts) && entry.side === "credit"))
          );
        }).length
      };

      if (!stage1IsValid) {
        stage1Result.discrepancies = await AccountingValidationService._findDirectDebitStage1Discrepancies(
          stage1DirectDebits,
          ledgerEntries
        );
      }

      // Stage 2 Validation: Direct debit settlement (WealthKernel settled)
      const stage2DirectDebits = await DepositCashTransaction.find({
        "consideration.currency": "EUR",
        depositMethod: { $in: ["DIRECT_DEBIT", "DIRECT_DEBIT_AND_BANK_TRANSFER"] },
        "providers.wealthkernel.status": "Settled",
        "providers.wealthkernel.settledAt": { $gte: new Date(fromDate) },
        _id: { $nin: IGNORED_DEPOSIT_IDS }
      }).populate("owner");

      const stage2DbTotalAmount = stage2DirectDebits
        .reduce((sum, deposit) => {
          const amount = new Decimal(deposit.consideration.amount || 0).div(100);
          return sum.plus(amount);
        }, new Decimal(0))
        .toNumber();

      const stage2DepositIds = new Set(stage2DirectDebits.map((d) => d._id.toString()));

      // Check debits on omnibus account for direct debit stage 2
      const stage2DebitAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2DepositIds.has(transactionId) &&
            entry.side === "debit" &&
            entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      // Check credits on intermediary deposits account for stage 2
      const stage2CreditAmount = ledgerEntries
        .filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2DepositIds.has(transactionId) &&
            entry.side === "credit" &&
            entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1
          );
        })
        .reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0))
        .toNumber();

      const stage2DebitDifference = new Decimal(stage2DbTotalAmount).minus(stage2DebitAmount).toNumber();
      const stage2CreditDifference = new Decimal(stage2DbTotalAmount).minus(stage2CreditAmount).toNumber();
      const stage2IsValid = stage2DebitDifference === 0 && stage2CreditDifference === 0;

      const stage2Result: ValidationResult = {
        transactionType: "deposits_direct_debit_stage2",
        isValid: stage2IsValid,
        dbTotalAmount: stage2DbTotalAmount,
        ledgerTotalAmount: stage2DebitAmount,
        difference: stage2DebitDifference,
        transactionCount: stage2DirectDebits.length,
        ledgerEntryCount: ledgerEntries.filter((entry) => {
          const transactionId = entry.document_id;
          return (
            stage2DepositIds.has(transactionId) &&
            ((entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS && entry.side === "debit") ||
              (entry.account_code === LedgerAccounts.INTERMEDIARY_DEPOSITS_1 && entry.side === "credit"))
          );
        }).length
      };

      if (!stage2IsValid) {
        stage2Result.discrepancies = await AccountingValidationService._findDirectDebitStage2Discrepancies(
          stage2DirectDebits,
          ledgerEntries
        );
      }

      logger.info("Direct debit deposits validation completed", {
        module: "AccountingValidationService",
        method: "_validateDirectDebitDeposits",
        data: { stage1Result, stage2Result }
      });

      return [stage1Result, stage2Result];
    } catch (error) {
      captureException(error);
      logger.error("Error during direct debit deposits validation", {
        module: "AccountingValidationService",
        method: "_validateDirectDebitDeposits",
        data: { error }
      });
      return [];
    }
  }

  /**
   * Finds individual discrepancies between direct debit stage 1 deposits and their ledger entries
   */
  private static async _findDirectDebitStage1Discrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];
    const clientAccountCodes = new Set([
      LedgerAccounts.CLIENT_DOMESTIC,
      LedgerAccounts.CLIENT_EU_EEA,
      LedgerAccounts.CLIENT_INTERNATIONAL
    ]);

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = new Decimal(deposit.consideration.amount || 0).div(100).toNumber();

      // Find credit entries to client accounts for this transaction
      const creditEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "credit" &&
          clientAccountCodes.has(entry.account_code as LedgerAccounts)
        );
      });

      // Sum credit amounts to client accounts
      const ledgerAmount = creditEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `MMF dividend commission: DB amount (€${dbAmount}) doesn't match revenue account debits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }

  /**
   * Finds individual discrepancies between direct debit stage 2 deposits and their ledger entries
   */
  private static async _findDirectDebitStage2Discrepancies(
    deposits: any[],
    ledgerEntries: LedgerQueryResult[]
  ): Promise<ValidationDiscrepancy[]> {
    const discrepancies: ValidationDiscrepancy[] = [];

    for (const deposit of deposits) {
      const transactionId = deposit._id.toString();
      const dbAmount = new Decimal(deposit.consideration.amount || 0).div(100).toNumber();

      // Find debit entries to omnibus account for this transaction
      const debitEntries = ledgerEntries.filter((entry) => {
        const entryTransactionId = entry.document_id;
        return (
          entryTransactionId === transactionId &&
          entry.side === "debit" &&
          entry.account_code === LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS
        );
      });

      // Sum debit amounts to omnibus account
      const ledgerAmount = debitEntries.reduce((sum, entry) => sum.plus(entry.amount), new Decimal(0)).toNumber();

      const difference = new Decimal(dbAmount).minus(ledgerAmount).toNumber();

      if (Math.abs(difference) > 0) {
        const description = `MMF dividend commission: DB amount (€${dbAmount}) doesn't match revenue account debits (€${ledgerAmount})`;

        discrepancies.push({
          transactionId,
          dbAmount,
          ledgerAmount,
          difference,
          description
        });
      }
    }

    return discrepancies;
  }
}
