import { captureException } from "@sentry/node";
import { TransactionService } from "../../services/transactionService";
import {
  AssetTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  SavingsDividendTransaction,
  SavingsTopupTransaction,
  SavingsWithdrawalTransaction,
  StockSplitTransaction,
  StockSplitTransactionDocument
} from "../../models/Transaction";
import { TransferWithIntermediaryStageEnum } from "../../types/transactions";
import logger from "../../external-services/loggerService";
import { UserDocument } from "../../models/User";
import SavingsProductService from "../../services/savingsProductService";
import DateUtil from "../../utils/dateUtil";
import { TransactionType, WealthkernelService } from "../../external-services/wealthkernelService";
import Decimal from "decimal.js";
import {
  AutomationDocument,
  SavingsTopUpAutomationDocument,
  TopUpAutomationDocument
} from "../../models/Automation";
import { MandateDocument } from "../../models/Mandate";
import { GoCardlessPaymentsService } from "../../external-services/goCardlessPaymentsService";
import { Payout } from "../../models/Payout";
import { ProviderEnum } from "../../configs/providersConfig";
import WealthkernelUtil from "../../utils/wealthkernelUtil";
import CorporateEventService from "../../services/corporateEventService";
import { StockSplitCorporateEventDocument } from "../../models/CorporateEvent";
import { PortfolioDocument } from "../../models/Portfolio";
import DbUtil from "../../utils/dbUtil";
import { adjustHoldingForSplit } from "../../utils/tickerUtil";
import PortfolioService from "../../services/portfolioService";
import mongoose from "mongoose";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PayoutRepository } from "../../repositories/payoutRepository";

const { ASSET_CONFIG } = investmentUniverseConfig;

const DAYS_BACK_TO_CREATE_TRANSACTIONS_FOR_SPLITS = 7;
const DIVIDENDS_TRACKING_DAYS = 5;
const DB_BATCH_SIZE = 1000;

export class TransactionCronService {
  public static async syncSavingsTopupTransactionsFromWK(): Promise<void> {
    const pendingSavingsTopupTransactions = await SavingsTopupTransaction.find({
      status: "Pending"
    }).populate("owner orders");

    for (const transaction of pendingSavingsTopupTransactions) {
      try {
        await TransactionService.syncSavingsTopupTransaction(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Transaction syncing failed for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "syncSavingsTopupTransactionsFromWK",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  public static async syncSavingsWithdrawalsTransactionsFromWK(): Promise<void> {
    const pendingSavingsWithdrawalsTransactions = await SavingsWithdrawalTransaction.find({
      status: "Pending"
    }).populate("owner orders");

    for (const transaction of pendingSavingsWithdrawalsTransactions) {
      try {
        await TransactionService.syncSavingsWithdrawalTransaction(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Transaction syncing failed for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "syncSavingsWithdrawalsTransactionsFromWK",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  public static async processPendingTopUpSavingsWithdrawals(): Promise<void> {
    const pendingTopUpSavingsWithdrawals = await SavingsWithdrawalTransaction.find({
      status: "PendingTopUp"
    }).populate("portfolio orders");

    logger.info(`Found ${pendingTopUpSavingsWithdrawals.length} PendingTopUp Savings Withdrawals`, {
      module: "TransactionService",
      method: "processPendingTopUpSavingsWithdrawals"
    });

    for (let i = 0; i < pendingTopUpSavingsWithdrawals.length; i++) {
      const pendingTopUpSavingsWithdrawal = pendingTopUpSavingsWithdrawals[i];

      try {
        await TransactionService.processPendingTopUpSavingsWithdrawal(pendingTopUpSavingsWithdrawal);
      } catch (err) {
        captureException(err);
        logger.error(`Failed to process PendingTopUp Savings Withdrawal ${pendingTopUpSavingsWithdrawal.id}.`, {
          module: "TransactionCronService",
          method: "processPendingTopUpSavingsWithdrawals",
          data: { pendingTopUpSavingsWithdrawal: pendingTopUpSavingsWithdrawal.id, error: err }
        });
      }
    }
  }

  public static async syncSavingsDividendsLinkedToSavingsTopup(): Promise<void> {
    const savingsDividends = await SavingsDividendTransaction.find({
      status: "PendingReinvestment"
    }).populate("linkedSavingsTopup");

    for (const savingsDividend of savingsDividends) {
      try {
        await TransactionService.syncSavingsDividendLinkedToSavingsTopup(savingsDividend);
      } catch (err) {
        captureException(err);
        logger.error(`Syncing ${savingsDividend.id} savings dividend with pending savings topup`, {
          module: "TransactionCronService",
          method: "syncSavingsDividendsLinkedToSavingsTopup"
        });
      }
    }
  }

  public static async createWealthkernelSavingsDividends(): Promise<void> {
    const isinSavingsProductDict = await SavingsProductService.getSavingsProductsDict("isin", false);

    /**
     * Get the start date for the dividends tracking.
     * If the start date is before the first day of the month, set it to the first day of the month.
     * --
     * This is to ensure that we only fetch dividends that are paid out in the current month.
     */
    const dividendTrackingDays = new Decimal(
      process.env.MMF_DIVIDENDS_TRACKING_DAYS ?? DIVIDENDS_TRACKING_DAYS
    ).toNumber();
    let startDate = DateUtil.getDateOfDaysAgo(new Date(), dividendTrackingDays);
    const firstDayOfTheMonth = DateUtil.getFirstDayOfThisMonth();
    if (startDate < firstDayOfTheMonth) {
      startDate = firstDayOfTheMonth;
    }

    await WealthkernelUtil.onEachInstance(async (wealthkernelService) => {
      await wealthkernelService.listTransactions(
        {
          startDate: WealthkernelService.formatDate(startDate),
          type: "Dividend",
          limit: 500
        },
        async (dividend: TransactionType): Promise<void> => {
          try {
            const savingsProduct = isinSavingsProductDict[dividend.isin as string];
            if (!savingsProduct) return;

            if (dividend.consideration.amount > 0) {
              await TransactionService.createSavingsDividend(dividend, savingsProduct);
            } else {
              logger.warn(`Cannot process savings dividend ${dividend.id} due to zero consideration amount`, {
                module: "TransactionCronService",
                method: "createWealthkernelSavingsDividends",
                data: { dividend }
              });
            }
          } catch (err) {
            captureException(err);
            logger.error(`Savings dividend creation for dividend ${dividend.id} failed`, {
              module: "TransactionCronService",
              method: "createWealthkernelSavingsDividends",
              data: { dividend, error: err }
            });
          }
        }
      );
    });
  }

  public static async reinvestSavingsDividends(): Promise<void> {
    const pendingSavingsDividends = await SavingsDividendTransaction.find({
      status: "Pending",
      linkedSavingsTopup: { $exists: false }
    }).populate("portfolio");

    for (const dividend of pendingSavingsDividends) {
      try {
        await TransactionService.reinvestSavingsDividend(dividend);
      } catch (err) {
        captureException(err);
        logger.error(`Savings dividend reinvestment for dividend ${dividend._id} failed`, {
          module: "TransactionCronService",
          method: "reinvestSavingsDividends",
          data: { dividendId: dividend._id, error: err }
        });
      }
    }
  }

  public static async processSavingsTopupsPendingDeposit(): Promise<void> {
    const pendingDepositSavingsTopupTransactions = await SavingsTopupTransaction.find({
      status: "PendingDeposit"
    }).populate("pendingDeposit");

    for (const transaction of pendingDepositSavingsTopupTransactions) {
      try {
        await TransactionService.processSavingsTopupPendingDeposit(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Transaction processing failed for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "processSavingsTopupsPendingDeposit",
          data: { transactionId: transaction._id, userId: (transaction.owner as UserDocument)._id, error: err }
        });
      }
    }
  }

  public static async createGoCardlessDirectDebitPayments(): Promise<void> {
    const depositsPendingGoCardlessPayment: DepositCashTransactionDocument[] = await DepositCashTransaction.find({
      linkedAutomation: { $exists: true },
      status: "Pending",
      "directDebit.providers.gocardless.id": { $exists: false },
      "directDebit.activeProviders": ProviderEnum.GOCARDLESS
    }).populate([
      {
        path: "linkedAutomation",
        populate: {
          path: "mandate"
        }
      }
    ]);

    const eligibleDeposits = depositsPendingGoCardlessPayment
      .filter((deposit) => (deposit.linkedAutomation as AutomationDocument).active)
      .filter((deposit) => {
        const automation = deposit.linkedAutomation as TopUpAutomationDocument | SavingsTopUpAutomationDocument;
        const mandate = automation.mandate as MandateDocument;

        return ["Active", "Pending"].includes(mandate.status);
      });

    for (const transaction of eligibleDeposits) {
      try {
        await TransactionService.createGoCardlessDirectDebitPayment(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Failed to create GoCardless direct-debit payment for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "createGoCardlessDirectDebitPayments",
          data: { transactionId: transaction._id, userId: transaction.owner._id, error: err }
        });
      }
    }
  }

  public static async createWealthkernelDirectDebitPayments(): Promise<void> {
    const depositsPendingWkPayment: DepositCashTransactionDocument[] = await DepositCashTransaction.find({
      linkedAutomation: { $exists: true },
      status: "Pending",
      "directDebit.providers.wealthkernel.id": { $exists: false },
      "directDebit.activeProviders": ProviderEnum.WEALTHKERNEL
    }).populate([
      { path: "portfolio" },
      {
        path: "linkedAutomation",
        populate: {
          path: "mandate"
        }
      }
    ]);

    const eligibleDeposits = depositsPendingWkPayment
      .filter((deposit) => (deposit.linkedAutomation as AutomationDocument).active)
      .filter((deposit) => {
        const automation = deposit.linkedAutomation as TopUpAutomationDocument | SavingsTopUpAutomationDocument;
        const mandate = automation.mandate as MandateDocument;
        return mandate.status === "Active";
      });

    for (const transaction of eligibleDeposits) {
      try {
        await TransactionService.createWealthkernelDirectDebitPayment(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Failed to create Wealthkernel direct-debit payment for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "createWealthkernelDirectDebitPayments",
          data: { transactionId: transaction._id, userId: transaction.owner._id, error: err }
        });
      }
    }
  }

  /**
   * @description Creates Devengo payments to users' Wealthkernel bank accounts for all eligible payout items.
   */
  public static async createPaymentsForEligiblePayouts(): Promise<void> {
    const payoutsWithConfirmedIncomingPayments = await Payout.find({
      status: "Pending",
      "providers.devengo.status": "confirmed"
    });

    for (let i = 0; i < payoutsWithConfirmedIncomingPayments.length; i++) {
      const payout = payoutsWithConfirmedIncomingPayments[i];

      try {
        const payoutItems = await GoCardlessPaymentsService.Instance.retrievePayoutItems(
          payout.providers.gocardless.id
        );

        // We first retrieve all the deposits that belong to this payout.
        const paymentIds = payoutItems
          .filter((item) => item.type === "payment_paid_out")
          .map((item) => item.links.payment);
        const deposits = await TransactionService.getDirectDebitDepositsByGoCardlessIds(paymentIds);

        await Promise.all(
          deposits
            .filter(
              (deposit) => !deposit.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.id
            )
            .map(async (deposit) =>
              TransactionService.createBankTransferPaymentAndUpdateDeposit(
                deposit,
                TransferWithIntermediaryStageEnum.COLLECTION
              )
            )
        );

        if (
          deposits.every(
            (deposit) => deposit.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo.id
          )
        ) {
          await PayoutRepository.completePayout(payout.id);
        }
      } catch (err) {
        captureException(err);
        logger.error(`Payout reconciliation creation failed for payout ${payout._id}`, {
          module: "TransactionCronService",
          method: "createPaymentsForEligiblePayouts",
          data: { error: err }
        });
      }
    }
  }

  public static async processAssetTransactionsPendingDeposit(): Promise<void> {
    const pendingDepositAssetTransactions = await AssetTransaction.find({
      status: "PendingDeposit"
    }).populate("pendingDeposit");

    for (const transaction of pendingDepositAssetTransactions) {
      try {
        await TransactionService.processAssetTransactionPendingDeposit(transaction);
      } catch (err) {
        captureException(err);
        logger.error(`Transaction processing failed for ${transaction._id}`, {
          module: "TransactionCronService",
          method: "processAssetTransactionsPendingDeposit",
          data: { transactionId: transaction._id, userId: transaction.owner._id, error: err }
        });
      }
    }
  }

  public static async createStockSplitTransactions(): Promise<void> {
    const stockSplits = await CorporateEventService.getLastNDaysStockSplits(
      DAYS_BACK_TO_CREATE_TRANSACTIONS_FOR_SPLITS
    );

    await Promise.all(
      stockSplits.map(async (stockSplit) => {
        try {
          return TransactionCronService._createAllTransactionsForStockSplit(stockSplit);
        } catch (err) {
          logger.error(`Creating transactions for stock split ${stockSplit.asset} - ${stockSplit.date} failed!`, {
            module: "TransactionCronService",
            method: "createStockSplitTransactions"
          });
          captureException(err);
        }
      })
    );
  }

  public static async processStockSplitTransactions(): Promise<void> {
    const pendingStockSplitTransactions = await TransactionService.getPendingStockSplitTransactions();

    await Promise.all(
      pendingStockSplitTransactions.map(async (transaction) => {
        try {
          return TransactionCronService._processStockSplitTransaction(transaction);
        } catch (err) {
          logger.error(`Processing stock split transaction ${transaction.id} failed!`, {
            module: "TransactionCronService",
            method: "processStockSplitTransactions"
          });
          captureException(err);
        }
      })
    );
  }

  private static async _createAllTransactionsForStockSplit(stockSplit: StockSplitCorporateEventDocument) {
    await PortfolioService.getPortfoliosStreamed({
      hasHolding: stockSplit.asset as investmentUniverseConfig.AssetType
    }).eachAsync(
      async (portfolios) => {
        const dbOperations = portfolios.map((portfolio) => ({
          updateOne: {
            filter: {
              portfolio: portfolio.id,
              stockSplit: stockSplit.id
            },
            update: {
              $setOnInsert: {
                owner: portfolio.owner as mongoose.Types.ObjectId,
                portfolio: portfolio.id,
                stockSplit: stockSplit.id,
                createdAt: new Date()
              }
            },
            upsert: true
          }
        }));

        await StockSplitTransaction.bulkWrite(dbOperations);
      },
      {
        batchSize: DB_BATCH_SIZE
      }
    );
  }

  private static async _processStockSplitTransaction(transaction: StockSplitTransactionDocument) {
    await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
      const portfolio = transaction.portfolio as PortfolioDocument;
      const stockSplit = transaction.stockSplit as StockSplitCorporateEventDocument;

      const currentQuantity = portfolio.holdings.find(
        (holding) => holding.assetCommonId === stockSplit.asset
      ).quantity;
      const adjustedQuantity = adjustHoldingForSplit(currentQuantity, stockSplit);

      return Promise.all([
        PortfolioService.updatePortfolioHoldings(
          portfolio.id,
          [
            {
              side: currentQuantity > adjustedQuantity ? "Sell" : "Buy",
              quantity: Decimal.sub(currentQuantity, adjustedQuantity).abs().toNumber(),
              isin: ASSET_CONFIG[stockSplit.asset as investmentUniverseConfig.AssetType].isin
            }
          ],
          { session }
        ),
        StockSplitTransaction.findByIdAndUpdate(
          transaction.id,
          {
            status: "Settled",
            settledAt: new Date(Date.now())
          },
          { session }
        )
      ]);
    });
  }
}
